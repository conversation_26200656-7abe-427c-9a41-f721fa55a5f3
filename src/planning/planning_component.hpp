// Copyright (c) 2025 T2 Inc. All rights reserved.

#pragma once

#include <optional>

#include <canbus_msgs/msg/chassis.hpp>                   // Chassis
#include <perception_msgs/msg/perception_obstacles.hpp>  // PerceptionObstacles
#include <prediction_msgs/msg/prediction_obstacles.hpp>  // PredictionObstacles
#include <rclcpp/rclcpp.hpp>                             // rclcpp::Node

#include "grace/execution/executor2/include/executor2/apex_node_base.hpp"  //
// ::apex::executor::apex_node_base
// ::apex::executor::executable_item

// Inter-component planning messages
#include <localization_msgs/msg/localization_estimate.hpp>  // LocalizationEstimate
#include <planning_msgs/msg/adc_trajectory.hpp>             // ADCTrajectory
#include <planning_msgs/msg/obstacle.hpp>                   // Obstacle
#include <planning_msgs/msg/planning_request.hpp>           // PlanningRequest

// Intra-component planning messages
#include <planning_msgs/msg/dummy_message.hpp>     // DummyMessage
#include <planning_msgs/msg/update_parameter.hpp>  // UpdateParameter

#include "src/common/health/health_reporter.hpp"        // HealthReporter
#include "src/planning/common/input_message_info.hpp"   // ChassisInfo, LocalizationInfo
#include "src/planning/common/intention_task_data.hpp"  // IntentionTaskData
#include "src/planning/common/obstacle.hpp"             // Obstacle
#include "src/planning/common/planning_vehicle_state/planning_vehicle_state.hpp"  // PlanningVehicleState
#include "src/planning/common/trajectory/trajectory.hpp"         // PlanningTrajectory
#include "src/planning/config/planning_config.hpp"               // PlanningConfiguration
#include "src/planning/create_intentions/create_intentions.hpp"  // CreateIntentions
#include "src/planning/lane_change_decider/driver_trigger/driver_trigger.hpp"  // DriverTrigger
#include "src/planning/lane_change_decider/lane_change_decider.hpp"            // LaneChangeDecider
#include "src/planning/lane_change_decider/lane_change_decision.hpp"           // LaneChangeDecision
#include "src/planning/planner/planner.hpp"                                    // Planner
#include "src/planning/planning_dir_prefix.hpp"            // FULL_PLANNING_DIR_PREFIX
#include "src/planning/planning_macros.hpp"                // REGISTER_*_PLANNING_MSG
#include "src/planning/reference_line/reference_line.hpp"  // ReferenceLine, ReferenceLineAndRouteSegments
#include "src/planning/reference_line/reference_line_provider.hpp"  // ReferenceLineProvider
#include "src/planning/select_trajectory/select_trajectory.hpp"     // SelectTrajectory

namespace t2::planning {

REGISTER_INTER_PERCEPTION_MSG(PerceptionObstacles);
REGISTER_INTER_PREDICTION_MSG(PredictionObstacles);

// using PredictionObstacles = ::prediction_msgs::msg::PredictionObstacles;

REGISTER_INTER_LOCALIZATION_MSG(LocalizationEstimate);
REGISTER_INTER_CHASSIS_MSG(Chassis);
REGISTER_INTER_CHASSIS_MSG(DrivingMode);

// Inter-component planning messages
REGISTER_INTER_PLANNING_MSG(ADCTrajectory);
REGISTER_INTER_PLANNING_MSG(PlanningRequest);
REGISTER_INTER_PLANNING_MSG(PlanningModuleStatus);

// Intra-component planning messages
REGISTER_INTER_PLANNING_MSG(UpdateParameter);

class PlanningComponent : public
                          // ::apex::executor::executable_item
                          ::apex::executor::apex_node_base {
  using base = ::apex::executor::apex_node_base;
  // ::apex::executor::executable_item;
  using HealthReporter = ::t2::common::health::HealthReporter;

 public:
  PlanningComponent(
      // ::apex::executor::executor_base& executor
      const std::shared_ptr<HealthReporter>& health_reporter_ptr);
  ~PlanningComponent();
  rclcpp::Node& node_;

  void WaitForMatched(const std::size_t expected_num_subscriptions,
                      const std::chrono::nanoseconds timeout = std::chrono::nanoseconds::max())

  {
    trajectory_writer_->wait_for_matched(expected_num_subscriptions, timeout);
  }

  void PublishDummyTrajectory();

  ADCTrajectory ProcHelper(const PredictionObstacles& prediction_obstacles, const Chassis& chassis,
                           const LocalizationEstimate&
                               localization_estimate);  ///< for planning closed loop without sils

 private:
  /**
   * @brief Process of the object tracking. This method is periodically called
   * by cyber node manager at everytime when perception results from fusion node
   * are published.
   *
   * @param msg perception results from fusion perception node.
   */
  void Proc(const PredictionObstacles& prediction_obstacles, const Chassis& chassis,
            const LocalizationEstimate& localization_estimate);

  void SetupCommunication();

  /* ===== Required Apex functions ===== */
  bool execute_impl() override;
  ::apex::executor::subscription_list get_triggering_subscriptions_impl() const override;
  ::apex::executor::subscription_list get_non_triggering_subscriptions_impl() const override;
  ::apex::executor::publisher_list get_publishers_impl() const override;

  /* ========== Test framework APIs ========== */
  ADCTrajectory ProcInternal_(const std::map<int, Obstacle>& obstacle_map,
                              const ChassisInfo& chassis_info,
                              const LocalizationInfo& localization_info);
  const auto& planner() const { return planner_; }
  auto& config() { return config_; }

  /* ===== Readers =====*/
  ::rclcpp::PollingSubscription<LocalizationEstimate>::SharedPtr localization_reader_;
  ::rclcpp::PollingSubscription<PredictionObstacles>::SharedPtr prediction_obstacles_reader_;
  // ::rclcpp::PollingSubscription<PerceptionObstacles>::SharedPtr perception_obstacles_reader_;
  ::rclcpp::PollingSubscription<Chassis>::SharedPtr chassis_reader_;

  ::rclcpp::PollingSubscription<PlanningRequest>::SharedPtr planning_request_reader_;
  // ::rclcpp::PollingSubscription<prediction::PredictionObstacles>::SharedPtr prediction_reader_;
  ::rclcpp::PollingSubscription<UpdateParameter>::SharedPtr
      update_parameter_reader_;  ///< receive update parameter

  /* ===== Writers =====*/
  ::rclcpp::Publisher<ADCTrajectory>::SharedPtr trajectory_writer_;

  /* ===== END OF READERS, WRITERS =====*/
  // ::apex::executor::executor_base& executor_;
  std::shared_ptr<HealthReporter> health_reporter_ptr_;
  HealthReporter& GetHealthReporter();

  // t2::common::exec_time_profile::ExecutionTimeProfiler exec_time_profiler_{};

  PlanningRequest planning_request_;  ///< workspace variable to obtain message
                                      ///< from ADStateManager
  PlanningConfiguration config_;      ///< planning configurations

  std::shared_ptr<IntentionTaskData>
      last_trajectory_intention_ptr_;   ///< intention from
                                        ///< drive_intention_task_data, i.e.,
                                        ///< the intention task data with the
                                        ///< best intention
  PlanningTrajectory prev_trajectory_;  ///< last publishable trajectory output
                                        ///< from ComputeTrajectory

  PlanningModuleStatus planning_module_status_;

  Planner planner_;  ///< contains ScenarioManager that manages Scenarios
  IntentionCreator intention_creator_;
  SelectTrajectory select_trajectory_;
  ReferenceLineProvider reference_line_provider_;  ///< generates reference lines from route
  //                                ///< segments and then smoothens reference
  //                                ///< lines

  lane_change_decider::LaneChangeDecision lane_change_decision_;

  // Speed limiter Driver Trigger
  Advice engage_advice_;
  double speed_limit_driver_trigger_value_ = 0.0;

  ReferenceLineAndRouteSegments lc_source_lane_;  ///< lane change source reference line
  std::optional<ReferenceLineAndRouteSegments>
      opt_lc_destination_lane_;  ///< lane change target reference line

  lane_change_trigger::DriverTrigger lane_change_driver_trigger_;
  lane_change_decider::LaneChangeDecider lane_change_decider_;

  // HMI
  // std::optional<HMINotification> stored_hmi_notification_;
  // std::optional<HMINotification> prev_hmi_notification_;
  std::optional<double> hmi_pub_time_;

  // Speed limit trigger
  bool target_speed_anticipation_enable_ = false;

 private:
  auto Preprocessing(ADCTrajectory& output_trajectory, const PlanningConfiguration& config,
                     const PlanningTrajectory& prev_trajectory,
                     ReferenceLineProvider& reference_line_provider,
                     const LocalizationInfo& localization_info,
                     const ChassisInfo& chassis_info) const
      -> std::tuple<std::vector<TrajectoryPoint>, std::string, PlanningVehicleState,
                    PlanningVehicleState, PlanningVehicleState, double, double>;

  /**
   * @brief Main function that plans a trajectory.
   *
   * This function runs the planning algorithm and returns the planned
   * trajectory.
   *
   * @param[out] opt_frame the current workspace frame used for planning
   * @param[out] status status that indicates whether planning succeeds
   * @param[out] output_trajectory The output ADCTrajectory object that stores
   * the planned trajectory.
   * @param[out] last_task_status The status of the last planning task.
   * @param[out] last_trajectory_intention_ptr The pointer to the last
   * trajectory intention.
   * @param[out] prev_trajectory The pointer to the last publishable
   * trajectory.
   * @param[out] prev_speed_trajectory The pointer to the last speed
   * publishable trajectory.
   * @param[out] planner The planner object used for planning.
   * @param[out] reference_line_provider The reference line provider object.
   * @param[out] intention_cost_decider The intention cost decider object.
   * @param[out] planning_state Internal planning state regarding MRM and
   * recovery
   * @param planning_request planning request from ADStateManager (ADSM)
   * @param config The planning configuration.
   * @param local_view The local view of the environment.
   * @param injector The dependency injector object.
   * @return True if the planning is successful, false otherwise.
   */
  ADCTrajectory PlanningPipeline(
      std::shared_ptr<IntentionTaskData>& last_trajectory_intention_ptr,
      PlanningTrajectory& prev_trajectory, Planner& planner, IntentionCreator& intention_creator,
      SelectTrajectory& select_trajectory, ReferenceLineProvider& reference_line_provider,
      /* ========== */
      const PlanningRequest& planning_request, const PlanningConfiguration& config,
      /* ===== input to planning component ===== */
      const std::map<int, Obstacle>& obstacle_map, const ChassisInfo& chassis,
      const LocalizationInfo& localization_info,
      /* ========== */
      lane_change_decider::LaneChangeDecision& lane_change_decision, Advice& engage_advice,
      double& speed_limit_driver_trigger_value, PlanningModuleStatus& planning_module_status,
      ReferenceLineAndRouteSegments& lc_source_lane,
      std::optional<ReferenceLineAndRouteSegments>& opt_lc_destination_lane,
      lane_change_decider::LaneChangeDecider& lane_change_decider,
      lane_change_trigger::DriverTrigger& lane_change_driver_trigger,
      // std::optional<HMINotification>& prev_hmi_notification,
      // std::optional<HMINotification>& stored_hmi_notification,
      bool& target_speed_anticipation_enable) const;

  /**
   * @brief Creates a time-aligned vehicle state.
   *
   * @param start_timestamp time stamp.
   * @param local_view The local view.
   * @param injector The dependency injector.
   *
   * @return local vehicle state and the status result.
   */
  auto CreateAlignedVehicleState(double start_timestamp,
                                 const PlanningVehicleState& original_vehicle_state) const
      -> std::pair<PlanningVehicleState, bool>;

  /**
   * @brief Projects the base vehicle state to the previous trajectory.
   *
   * This function takes the current vehicle state and projects it onto the
   * previous trajectory. The projected vehicle state is then used to generate a
   * publishable trajectory.
   *
   * @param[out] vehicle_state The current vehicle state.
   * @param prev_trajectory A pointer to the last publishable
   * trajectory.
   */
  void ProjectVehicleIntoLastTrajectory(PlanningVehicleState& vehicle_state,
                                        const std::vector<TrajectoryPoint>& prev_trajectory) const;

  std::optional<ReferenceLineAndRouteSegments> GetCurrentDrivingLane(
      const std::list<ReferenceLine>& list_reference_lines,
      const std::list<route_lane_manager::RouteSegments>& list_route_segments,
      const PlanningVehicleState& vehicle_state) const;

  ReferenceLineMap GetCurrentAndAdjacentLanes(
      const std::list<ReferenceLine>& list_reference_lines,
      const std::list<route_lane_manager::RouteSegments>& list_route_segments,
      const PlanningVehicleState& vehicle_state) const;

  // static void SendPlanningActionEndNotification(
  //     t2::adapter::WriterChannelPtr<PlanningActionEndNotification>&
  //         planning_action_end_notification_writer);

  void GetCurrentLaneInfoPtrFromReferenceLine(const PlanningVehicleState& vehicle_state,
                                              const ReferenceLine& reference_line) const;

  /*===== Input Topics =====*/
  static constexpr char const* kChassisTopic = "/t2/canbus/chassis";
  static constexpr char const* kLocalizationPoseTopic = "/t2/localization/pose";
  static constexpr char const* kPredictionTopic = "/t2/prediction";
  static constexpr char const* kPerceptionObstacleTopic = "/t2/perception/obstacles";

  static constexpr char const* kPlanningRequestTopic = "/t2/planning/planning_request";
  static constexpr char const* kPlanningResponseTopic = "/t2/planning/planning_response";

  static constexpr char const* kPlanningActionEndNotificationTopic =
      "/t2/planning/planning_action_end_notification";

  static constexpr char const* kHMINotificationTopic = "/hmi/notifications";

  static constexpr char const* kUpdateParameterTopic = "/t2/planning/update_parameter";
  static constexpr char const* kPlanningTopic = "/t2/planning";

  static constexpr char const* kPlanningComponentName = "planning_component";
  static constexpr char const* kPlanningNamespace = "planning_namespace";

  /*===== File Paths =====*/
  static constexpr char const* kPlannerConfigFile = PLANNING_DIR_PREFIX "conf/planner_config.json";

  static constexpr char const* kPlanningConfigFile =
      PLANNING_DIR_PREFIX "conf/planning_config.json";

  static constexpr char const* kPlanningGoogleProfilingFile = "data/log/PlanningComponent.prof";

  static constexpr char const* kPlanningEasyProfilerFile = "data/log/planning.prof";
};
}  // namespace t2::planning
