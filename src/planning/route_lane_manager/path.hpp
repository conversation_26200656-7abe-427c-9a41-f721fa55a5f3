// Copyright (c) 2025 T2 Inc. All rights reserved.

#pragma once

#include <string>
#include <utility>
#include <vector>

#include <planning_trajectory_msgs/msg/path_point.hpp>  // PathPoint

#include "src/common/math/box2d.hpp"
#include "src/common/math/line_segment2d.hpp"
#include "src/common/math/vec2d.hpp"
#include "src/map/hdmap/hdmap.hpp"
#include "src/map/hdmap/hdmap_common.hpp"
#include "src/map/hdmap/hdmap_util.hpp"
#include "src/map/proto/map_lane.pb.h"

namespace t2::planning::route_lane_manager {
using PathPoint = ::planning_trajectory_msgs::msg::PathPoint;

using t2::map::hdmap::LaneBoundaryType;
using t2::map::hdmap::LaneInfoConstPtr;
using t2::map::hdmap::OverlapInfoConstPtr;

struct LaneWaypoint {
  LaneWaypoint() = default;
  LaneWaypoint(LaneInfoConstPtr lane_arg, const double s_arg)
      : lane(CHECK_NOTNULL(lane_arg)), s(s_arg) {}
  LaneWaypoint(LaneInfoConstPtr lane_arg, const double s_arg, const double l_arg)
      : lane(CHECK_NOTNULL(lane_arg)), s(s_arg), l(l_arg) {}
  LaneInfoConstPtr lane = nullptr;
  double s = 0.0;
  double l = 0.0;

  std::string DebugString() const;
};

/**
 * @brief get left boundary type at a waypoint.
 */
LaneBoundaryType::Type LeftBoundaryType(const LaneWaypoint& waypoint);

/**
 * @brief get left boundary type at a waypoint.
 */
LaneBoundaryType::Type RightBoundaryType(const LaneWaypoint& waypoint);

struct LaneSegment {
  LaneSegment() = default;
  LaneSegment(LaneInfoConstPtr lane_arg, const double start_s_arg, const double end_s_arg)
      : lane(CHECK_NOTNULL(lane_arg)), start_s(start_s_arg), end_s(end_s_arg) {}
  LaneInfoConstPtr lane = nullptr;
  double start_s = 0.0;
  double end_s = 0.0;
  double Length() const { return end_s - start_s; }

  /**
   * Join neighboring lane segments if they have the same lane id
   */
  static void Join(std::vector<LaneSegment>* segments);

  std::string DebugString() const;
};

struct PathOverlap {
  PathOverlap() = default;
  PathOverlap(std::string object_id_arg, const double start_s_arg, const double end_s_arg)
      : object_id(std::move(object_id_arg)), start_s(start_s_arg), end_s(end_s_arg) {}

  std::string object_id;
  double start_s = 0.0;
  double end_s = 0.0;

  std::string DebugString() const;
};

class MapPathPoint {
 public:
  MapPathPoint() = default;

  explicit MapPathPoint(const PathPoint& point) : path_point_(point) {}

  MapPathPoint(const PathPoint& point, LaneWaypoint lane_waypoint) : path_point_(point) {
    lane_waypoints_.emplace_back(std::move(lane_waypoint));
  }

  MapPathPoint(const PathPoint& point, std::vector<LaneWaypoint> lane_waypoints)
      : path_point_(point), lane_waypoints_(std::move(lane_waypoints)) {}

  double x() const { return path_point_.x; }
  double y() const { return path_point_.y; }
  double heading() const { return path_point_.theta; }
  double kappa() const { return path_point_.kappa; }
  double dkappa() const { return path_point_.dkappa; }
  void set_x(const double x) { path_point_.x = x; }
  void set_y(const double y) { path_point_.y = y; }
  void set_heading(const double heading) { path_point_.theta = heading; }
  void set_kappa(const double kappa) { path_point_.kappa = kappa; }
  void set_dkappa(const double dkappa) { path_point_.dkappa = dkappa; }

  double DistanceTo(const t2::common::math::Vec2d& other) const {
    return std::hypot(path_point_.x - other.x(), path_point_.y - other.y());
  }

  double DistanceTo(const MapPathPoint& other) const {
    return std::hypot(path_point_.x - other.x(), path_point_.y - other.y());
  }

  double DistanceSquareTo(const t2::common::math::Vec2d& other) const {
    const double dx = path_point_.x - other.x();
    const double dy = path_point_.y - other.y();
    return dx * dx + dy * dy;
  }

  double DistanceSquareTo(const MapPathPoint& other) const {
    const double dx = path_point_.x - other.x();
    const double dy = path_point_.y - other.y();
    return dx * dx + dy * dy;
  }

  const std::vector<LaneWaypoint>& lane_waypoints() const { return lane_waypoints_; }

  void add_lane_waypoint(LaneWaypoint lane_waypoint) {
    lane_waypoints_.emplace_back(std::move(lane_waypoint));
  }
  void add_lane_waypoints(const std::vector<LaneWaypoint>& lane_waypoints) {
    lane_waypoints_.insert(lane_waypoints_.end(), lane_waypoints.begin(), lane_waypoints.end());
  }

  void clear_lane_waypoints() { lane_waypoints_.clear(); }

  static void RemoveDuplicates(std::vector<MapPathPoint>* points);

  PathPoint ToPathPoint(double s) const {
    PathPoint output_path_point = path_point_;
    output_path_point.s = s;
    return output_path_point;
  }

  static std::vector<MapPathPoint> GetPointsFromLane(const LaneInfoConstPtr& lane,
                                                     const double start_s, const double end_s);

  // Get a MapPathPoint for a lane at position s
  static std::optional<MapPathPoint> GetPointFromLane(const LaneInfoConstPtr& lane, const double s);

  std::string DebugString() const;

 protected:
  PathPoint path_point_;
  std::vector<LaneWaypoint> lane_waypoints_;
};

class Path;

class PathApproximation {
 public:
  PathApproximation() = default;
  PathApproximation(const Path& path, const double max_error)
      : max_error_(max_error), max_sqr_error_(max_error * max_error) {
    Init(path);
  }
  double max_error() const { return max_error_; }
  const std::vector<int>& original_ids() const { return original_ids_; }
  const std::vector<common::math::LineSegment2d>& segments() const { return segments_; }

  bool GetProjection(const Path& path, const t2::common::math::Vec2d& point, double* accumulate_s,
                     double* lateral, double* distance) const;

  bool OverlapWith(const Path& path, const t2::common::math::Box2d& box, double width) const;

 protected:
  void Init(const Path& path);
  bool is_within_max_error(const Path& path, const int s, const int t);
  double compute_max_error(const Path& path, const int s, const int t);

  void InitDilute(const Path& path);
  void InitProjections(const Path& path);

 protected:
  double max_error_ = 0;
  double max_sqr_error_ = 0;

  int num_points_ = 0;
  std::vector<int> original_ids_;
  std::vector<common::math::LineSegment2d> segments_;
  std::vector<double> max_error_per_segment_;

  // TODO(All): use direction change checks to early stop.

  // Projection of points onto the diluated segments.
  std::vector<double> projections_;
  double max_projection_;
  int num_projection_samples_ = 0;

  // The original_projection is the projection of original points onto the
  // diluated segments.
  std::vector<double> original_projections_;
  // max_p_to_left[i] = max(p[0], p[1], ... p[i]).
  // min_p_to_right[i] = min(p[i], p[i + 1], ... p[size - 1]).
  std::vector<double> max_original_projections_to_left_;
  std::vector<double> min_original_projections_to_right_;
  std::vector<int> sampled_max_original_projections_to_left_;
};

class InterpolatedIndex {
 public:
  InterpolatedIndex(int id_arg, double offset_arg) : id(id_arg), offset(offset_arg) {}
  int id = 0;
  double offset = 0.0;
};

class Path {
 public:
  Path() = default;
  explicit Path(const std::vector<MapPathPoint>& path_points);
  explicit Path(std::vector<MapPathPoint>&& path_points);
  explicit Path(std::vector<LaneSegment>&& path_points);
  explicit Path(const std::vector<LaneSegment>& path_points);

  Path(const std::vector<MapPathPoint>& path_points, const std::vector<LaneSegment>& lane_segments);
  Path(std::vector<MapPathPoint>&& path_points, std::vector<LaneSegment>&& lane_segments);

  Path(const std::vector<MapPathPoint>& path_points, const std::vector<LaneSegment>& lane_segments,
       const double max_approximation_error);
  Path(std::vector<MapPathPoint>&& path_points, std::vector<LaneSegment>&& lane_segments,
       const double max_approximation_error);

  // Return smooth coordinate by interpolated index or accumulate_s.
  MapPathPoint GetSmoothPoint(const InterpolatedIndex& index) const;
  MapPathPoint GetSmoothPoint(double s) const;

  // Compute accumulate s value of the index.
  double GetSFromIndex(const InterpolatedIndex& index) const;
  // Compute interpolated index by accumulate_s.
  InterpolatedIndex GetIndexFromS(double s) const;

  // get the index of the lane from s by accumulate_s
  InterpolatedIndex GetLaneIndexFromS(double s) const;

  std::vector<LaneSegment> GetLaneSegments(const double start_s, const double end_s) const;

  bool GetNearestPoint(const t2::common::math::Vec2d& point, double* accumulate_s,
                       double* lateral) const;
  bool GetNearestPoint(const t2::common::math::Vec2d& point, double* accumulate_s, double* lateral,
                       double* distance) const;
  bool GetProjectionWithHueristicParams(const t2::common::math::Vec2d& point,
                                        const double hueristic_start_s,
                                        const double hueristic_end_s, double* accumulate_s,
                                        double* lateral, double* min_distance) const;
  bool GetProjection(const t2::common::math::Vec2d& point, double* accumulate_s,
                     double* lateral) const;
  bool GetProjection(const t2::common::math::Vec2d& point, double* accumulate_s, double* lateral,
                     double* distance) const;

  bool GetHeadingAlongPath(const t2::common::math::Vec2d& point, double* heading) const;

  int num_points() const { return num_points_; }
  int num_segments() const { return num_segments_; }
  const std::vector<MapPathPoint>& path_points() const { return map_path_points_; }
  const std::vector<LaneSegment>& lane_segments() const { return lane_segments_; }
  const std::vector<LaneSegment>& lane_segments_to_next_point() const {
    return lane_segments_to_next_point_;
  }
  const std::vector<common::math::Vec2d>& unit_directions() const { return unit_directions_; }
  const std::vector<double>& accumulated_s() const { return accumulated_s_; }
  const std::vector<common::math::LineSegment2d>& segments() const { return segments_; }
  const PathApproximation* approximation() const { return &approximation_; }
  double length() const { return length_; }

  const PathOverlap* NextLaneOverlap(double s) const;

  const std::vector<PathOverlap>& lane_overlaps() const { return lane_overlaps_; }
  const std::vector<PathOverlap>& signal_overlaps() const { return signal_overlaps_; }
  const std::vector<PathOverlap>& yield_sign_overlaps() const { return yield_sign_overlaps_; }
  const std::vector<PathOverlap>& stop_sign_overlaps() const { return stop_sign_overlaps_; }
  const std::vector<PathOverlap>& crosswalk_overlaps() const { return crosswalk_overlaps_; }
  const std::vector<PathOverlap>& junction_overlaps() const { return junction_overlaps_; }
  const std::vector<PathOverlap>& pnc_junction_overlaps() const { return pnc_junction_overlaps_; }
  const std::vector<PathOverlap>& clear_area_overlaps() const { return clear_area_overlaps_; }
  const std::vector<PathOverlap>& speed_bump_overlaps() const { return speed_bump_overlaps_; }
  const std::vector<PathOverlap>& parking_space_overlaps() const { return parking_space_overlaps_; }

  double GetLaneLeftWidth(const double s) const;
  double GetLaneRightWidth(const double s) const;
  bool GetLaneWidth(const double s, double& lane_left_width, double& lane_right_width) const;

  double GetRoadLeftWidth(const double s) const;
  double GetRoadRightWidth(const double s) const;
  bool GetRoadWidth(const double s, double* road_left_width, double* road_ight_width) const;

  bool IsOnPath(const t2::common::math::Vec2d& point) const;
  bool OverlapWith(const t2::common::math::Box2d& box, double width) const;

  std::string DebugString() const;

  std::vector<MapPathPoint> map_path_points_;

 protected:
  void Init();
  void InitPoints();
  void InitLaneSegments();
  void InitWidth();
  void InitPointIndex();
  void InitOverlaps();

  double GetSample(const std::vector<double>& samples, const double s) const;

  using GetOverlapFromLaneFunc =
      std::function<const std::vector<OverlapInfoConstPtr>&(const t2::map::hdmap::LaneInfo&)>;
  void GetAllOverlaps(GetOverlapFromLaneFunc GetOverlaps_from_lane,
                      std::vector<PathOverlap>* const overlaps) const;

 protected:
  int num_points_ = 0;
  int num_segments_ = 0;
  std::vector<LaneSegment> lane_segments_;
  std::vector<double> lane_accumulated_s_;
  std::vector<LaneSegment> lane_segments_to_next_point_;
  std::vector<common::math::Vec2d> unit_directions_;
  double length_ = 0.0;
  std::vector<double> accumulated_s_;
  std::vector<common::math::LineSegment2d> segments_;
  bool use_path_approximation_ = false;
  PathApproximation approximation_;

  // Sampled every fixed length.
  int num_sample_points_ = 0;
  std::vector<double> lane_left_width_;
  std::vector<double> lane_right_width_;
  std::vector<double> road_left_width_;
  std::vector<double> road_right_width_;
  std::vector<int> last_point_index_;

  std::vector<PathOverlap> lane_overlaps_;
  std::vector<PathOverlap> signal_overlaps_;
  std::vector<PathOverlap> yield_sign_overlaps_;
  std::vector<PathOverlap> stop_sign_overlaps_;
  std::vector<PathOverlap> crosswalk_overlaps_;
  std::vector<PathOverlap> parking_space_overlaps_;
  std::vector<PathOverlap> junction_overlaps_;
  std::vector<PathOverlap> pnc_junction_overlaps_;
  std::vector<PathOverlap> clear_area_overlaps_;
  std::vector<PathOverlap> speed_bump_overlaps_;
};

}  // namespace t2::planning::route_lane_manager
