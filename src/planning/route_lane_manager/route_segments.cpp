// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "route_segments.hpp"

#include <algorithm>

#include "src/common/core/logging.hpp"

namespace t2::planning::route_lane_manager {
namespace {

// Minimum error in lane segmentation.
constexpr double kSegmentationEpsilon = 0.2;
}  // namespace

const std::string& RouteSegments::Id() const { return id_; }

void RouteSegments::SetId(const std::string& id) { id_ = id; }

bool RouteSegments::WithinLaneSegment(const LaneSegment& lane_segment,
                                      const LaneWaypoint& waypoint) {
  return waypoint.lane && lane_segment.lane->id().id() == waypoint.lane->id().id() &&
         lane_segment.start_s - kSegmentationEpsilon <= waypoint.s &&
         lane_segment.end_s + kSegmentationEpsilon >= waypoint.s;
}

bool RouteSegments::WithinLaneSegment(const LaneSegment& lane_segment,
                                      const planning::LaneWaypoint& waypoint) {
  return lane_segment.lane && lane_segment.lane->id().id() == waypoint.id() &&
         lane_segment.start_s - kSegmentationEpsilon <= waypoint.s() &&
         lane_segment.end_s + kSegmentationEpsilon >= waypoint.s();
}

bool RouteSegments::WithinLaneSegment(const planning::LaneSegment& lane_segment,
                                      const LaneWaypoint& waypoint) {
  return waypoint.lane && lane_segment.id() == waypoint.lane->id().id() &&
         lane_segment.start_s() - kSegmentationEpsilon <= waypoint.s &&
         lane_segment.end_s() + kSegmentationEpsilon >= waypoint.s;
}

bool RouteSegments::WithinLaneSegment(const planning::LaneSegment& lane_segment,
                                      const planning::LaneWaypoint& waypoint) {
  return lane_segment.id() == waypoint.id() &&
         lane_segment.start_s() - kSegmentationEpsilon <= waypoint.s() &&
         lane_segment.end_s() + kSegmentationEpsilon >= waypoint.s();
}

bool RouteSegments::Stitch(const RouteSegments& other) {
  auto first_waypoint = FirstWaypoint();
  bool has_overlap = IsWaypointOnSegment(other.FirstWaypoint());
  auto& other_lane_segments = other.lane_segments;
  if (other.IsWaypointOnSegment(first_waypoint)) {
    auto iter = other_lane_segments.begin();
    while (iter != other_lane_segments.end() && !WithinLaneSegment(*iter, first_waypoint)) {
      ++iter;
    }
    lane_segments.begin()->start_s = std::min(lane_segments.begin()->start_s, iter->start_s);
    lane_segments.begin()->end_s = std::max(lane_segments.begin()->end_s, iter->end_s);
    lane_segments.insert(lane_segments.begin(), other_lane_segments.begin(), iter);
    has_overlap = true;
  }
  auto last_waypoint = LastWaypoint();
  if (other.IsWaypointOnSegment(last_waypoint)) {
    auto iter = other_lane_segments.rbegin();
    while (iter != other_lane_segments.rend() && !WithinLaneSegment(*iter, last_waypoint)) {
      ++iter;
    }
    lane_segments.back().start_s = std::min(lane_segments.back().start_s, iter->start_s);
    lane_segments.back().end_s = std::max(lane_segments.back().end_s, iter->end_s);
    lane_segments.insert(lane_segments.end(), iter.base(), other_lane_segments.end());
    has_overlap = true;
  }
  return has_overlap;
}

LaneWaypoint RouteSegments::FirstWaypoint() const {
  return LaneWaypoint(lane_segments.front().lane, lane_segments.front().start_s, 0.0);
}

LaneWaypoint RouteSegments::LastWaypoint() const {
  return LaneWaypoint(lane_segments.back().lane, lane_segments.back().end_s, 0.0);
}

double RouteSegments::Length(const RouteSegments& segments) {
  double s = 0.0;
  for (const auto& seg : segments.lane_segments) {
    s += seg.Length();
  }
  return s;
}

bool RouteSegments::GetProjection(const t2::common::PointENU& point_enu, SLPoint* sl_point,
                                  LaneWaypoint* waypoint) const {
  return GetProjection({point_enu.x(), point_enu.y()}, sl_point, waypoint);
}

bool RouteSegments::Shrink(const t2::common::math::Vec2d& point, const double look_backward,
                           const double look_forward) {
  SLPoint sl_point;
  LaneWaypoint waypoint;
  if (!GetProjection(point, &sl_point, &waypoint)) {
    T2_ERROR << "failed to project " << point.DebugString() << " to segment";
    return false;
  }
  return Shrink(sl_point.s, look_backward, look_forward);
}

bool RouteSegments::Shrink(const double s, const double look_backward, const double look_forward) {
  LaneWaypoint waypoint;
  if (!GetWaypoint(s, &waypoint)) {
    return false;
  }
  return Shrink(s, waypoint, look_backward, look_forward);
}

bool RouteSegments::Shrink(const double s, const LaneWaypoint& waypoint, const double look_backward,
                           const double look_forward) {
  double acc_s = 0.0;
  auto iter = lane_segments.begin();
  while (iter != lane_segments.end() && acc_s + iter->Length() < s - look_backward) {
    acc_s += iter->Length();
    ++iter;
  }
  if (iter == lane_segments.end()) {
    return true;
  }
  iter->start_s = std::max(iter->start_s, s - look_backward - acc_s + iter->start_s);
  if (iter->Length() < kSegmentationEpsilon) {
    ++iter;
  }
  lane_segments.erase(lane_segments.begin(), iter);

  iter = lane_segments.begin();
  acc_s = 0.0;
  while (iter != lane_segments.end() && !WithinLaneSegment(*iter, waypoint)) {
    ++iter;
  }
  if (iter == lane_segments.end()) {
    return true;
  }
  acc_s = iter->end_s - waypoint.s;
  if (acc_s >= look_forward) {
    iter->end_s = waypoint.s + look_forward;
    ++iter;
    lane_segments.erase(iter, lane_segments.end());
    return true;
  }
  ++iter;
  while (iter != lane_segments.end() && acc_s + iter->Length() < look_forward) {
    acc_s += iter->Length();
    ++iter;
  }
  if (iter == lane_segments.end()) {
    return true;
  }
  iter->end_s = std::min(iter->end_s, look_forward - acc_s + iter->start_s);
  lane_segments.erase(iter + 1, lane_segments.end());
  return true;
}

bool RouteSegments::GetWaypoint(const double s, LaneWaypoint* waypoint) const {
  double accumulated_s = 0.0;
  bool has_projection = false;
  for (auto iter = lane_segments.begin(); iter != lane_segments.end();
       accumulated_s += (iter->end_s - iter->start_s), ++iter) {
    if (accumulated_s - kSegmentationEpsilon < s &&
        s < accumulated_s + iter->end_s - iter->start_s + kSegmentationEpsilon) {
      waypoint->lane = iter->lane;
      waypoint->s = s - accumulated_s + iter->start_s;
      if (waypoint->s < iter->start_s) {
        waypoint->s = iter->start_s;
      } else if (waypoint->s > iter->end_s) {
        waypoint->s = iter->end_s;
      }
      has_projection = true;
      break;
    }
  }
  return has_projection;
}

bool RouteSegments::GetProjection(const t2::common::math::Vec2d& point, SLPoint* sl_point,
                                  LaneWaypoint* waypoint) const {
  double min_l = std::numeric_limits<double>::infinity();
  double accumulated_s = 0.0;
  bool has_projection = false;
  for (auto iter = lane_segments.begin(); iter != lane_segments.end();
       accumulated_s += (iter->end_s - iter->start_s), ++iter) {
    double lane_s = 0.0;
    double lane_l = 0.0;
    if (!iter->lane->GetProjection(point, &lane_s, &lane_l)) {
      T2_ERROR << "Failed to get projection from point " << point.DebugString() << " on lane "
               << iter->lane->id().id();
      return false;
    }
    if (lane_s < iter->start_s - kSegmentationEpsilon ||
        lane_s > iter->end_s + kSegmentationEpsilon) {
      continue;
    }
    if (std::fabs(lane_l) < min_l) {
      has_projection = true;
      lane_s = std::max(iter->start_s, lane_s);
      lane_s = std::min(iter->end_s, lane_s);
      min_l = std::fabs(lane_l);
      sl_point->l = lane_l;
      sl_point->s = lane_s - iter->start_s + accumulated_s;
      waypoint->lane = iter->lane;
      waypoint->s = lane_s;
    }
  }
  return has_projection;
}

bool RouteSegments::IsWaypointOnSegment(const LaneWaypoint& waypoint) const {
  for (auto iter = lane_segments.begin(); iter != lane_segments.end(); ++iter) {
    if (WithinLaneSegment(*iter, waypoint)) {
      return true;
    }
  }
  return false;
}

}  // namespace t2::planning::route_lane_manager
