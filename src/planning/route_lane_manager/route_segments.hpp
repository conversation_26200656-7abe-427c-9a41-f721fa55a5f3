// Copyright (c) 2025 T2 Inc. All rights reserved.

#pragma once

#include <limits>
#include <string>
#include <vector>

#include <planning_trajectory_msgs/msg/sl_point.hpp>  // SLPoint

#include "path.hpp"
#include "src/map/hdmap/hdmap.hpp"
#include "src/planning/proto/routing.pb.h"

namespace t2::planning::route_lane_manager {

using SLPoint = ::planning_trajectory_msgs::msg::SLPoint;

/**
 * @brief class RouteSegments
 *
 * This class is a representation of the Passage type in routing.proto.
 * It is extended from a passage region, but keeps some properties of the
 * passage, such as the last end LaneWaypoint of the original passage region
 * (route_end_waypoint), whether the passage can lead to another passage in
 * routing (can_exit_).
 * This class contains the original data that can be used to generate
 * route_lane_manager::Path.
 **/
class RouteSegments {
 public:
  /**
   * The default constructor.
   **/
  RouteSegments() = default;

  /**
   * Project a point to this route segment.
   * @param point_enu a map point, or point, which is a Vec2d point
   * @param s return the longitudinal s relative to the route segment.
   * @param l return the lateral distance relative to the route segment.
   * @param waypoint return the LaneWaypoint, which has lane and lane_s on the
   * route segment.
   * @return false if error happened or projected outside of the lane segments.
   */
  bool GetProjection(const t2::common::PointENU& point_enu, SLPoint* sl_point,
                     LaneWaypoint* waypoint) const;
  bool GetProjection(const t2::common::math::Vec2d& point, SLPoint* sl_point,
                     LaneWaypoint* waypoint) const;

  bool GetWaypoint(const double s, LaneWaypoint* waypoint) const;

  /** Stitch current route segments with the other route segment.
   * Example 1
   * this:   |--------A-----x-----B------|
   * other:                 |-----B------x--------C-------|
   * Result: |--------A-----x-----B------x--------C-------|
   * In the above example, A-B is current route segments, and B-C is the other
   * route segments. We update current route segments to A-B-C.
   *
   * Example 2
   * this:                  |-----A------x--------B-------|
   * other:  |--------C-----x-----A------|
   * Result: |--------C-----x-----A------x--------B-------|
   * In the above example, A-B is current route segments, and C-A is the other
   * route segments. We update current route segments to C-A-B
   *
   * @return false if these two reference line cannot be stitched
   */
  bool Stitch(const RouteSegments& other);

  bool Shrink(const t2::common::math::Vec2d& point, const double look_backward,
              const double look_forward);

  bool Shrink(const double s, const double look_backward, const double look_forward);

  bool Shrink(const double s, const LaneWaypoint& waypoint, const double look_backward,
              const double look_forward);

  void SetId(const std::string& id);
  const std::string& Id() const;

  /**
   * Get the first waypoint from the lane segments.
   */
  LaneWaypoint FirstWaypoint() const;

  /**
   * Get the last waypoint from the lane segments.
   */
  LaneWaypoint LastWaypoint() const;

  /**
   * @brief Check if a waypoint is on segment
   */
  bool IsWaypointOnSegment(const LaneWaypoint& waypoint) const;

  static bool WithinLaneSegment(const LaneSegment& lane_segment, const LaneWaypoint& waypoint);

  static bool WithinLaneSegment(const LaneSegment& lane_segment,
                                const planning::LaneWaypoint& waypoint);

  static bool WithinLaneSegment(const planning::LaneSegment& lane_segment,
                                const LaneWaypoint& waypoint);

  static bool WithinLaneSegment(const planning::LaneSegment& lane_segment,
                                const planning::LaneWaypoint& waypoint);

  static double Length(const RouteSegments& segments);

  LaneWaypoint route_end_waypoint;  ///< This is the point that is the end of the
                                    ///< original passage in routing. It is used to check
                                    ///< if the vehicle is out of current routing. The
                                    ///< LaneWaypoint.lane is nullptr if the end of the
                                    ///< passage is not on the RouteSegment.

  bool is_on_segment = false;  ///< Indicates whether the vehicle is on current RouteSegment.

  /**
   * whether this segment can lead to another passage region in routing
   */
  bool can_exit = false;  ///< Whether the passage region that generate this route segment
                          ///< can lead to another passage region in route.

  bool stop_for_destination =
      false;  ///< Whether the vehicle should stop for destination. In a routing
              ///< that has loops, the adc may pass by destination many times,
              ///< but it only need to stop for destination  in the last loop.

  /**
   * Get the next change lane action need to take by the vehicle, if the vehicle
   * is on this RouteSegments.
   * --- If the vehicle does not need to change lane, then change_lane_type ==
   *     planning::routing::FORWARD;
   * --- If the vehicle need to change to left lane according to routing, then
   *     change_lane_type_ == planning::routing::LEFT;
   * --- If the vehicle need to change to right lane according to routing, then
   *     change_lane_type_ == planning::routing::RIGHT;
   */
  planning::ChangeLaneType next_action = planning::FORWARD;  ///< the next change lane action need
                                                             ///< to take by the vehicle, if the
                                                             ///< vehicle is on this RouteSegments.

  /**
   * Get the previous change lane action need to take by the vehicle to reach
   * current segment, if the vehicle is not on this RouteSegments.
   * If the vehicle is already on this segment, or does not need to change lane
   * to reach this segment, then change_lane_type = planning::routing::FORWARD;
   * If the vehicle need to change to left to reach this segment, then
   * change_lane_type_ =  planning::routing::LEFT;
   * If the vehicle need to change to right to reach this segment, then
   * change_lane_type_ = planning::routing::RIGHT;
   */
  planning::ChangeLaneType previous_action =
      planning::FORWARD;  ///< the previous change lane action need to
                          ///< take by the vehicle to reach current
                          ///< segment, if the vehicle is not on this
                          ///< RouteSegments.

 private:
  /**
   * Indicates whether current routeSegment is the neighbor of vehicle
   * routeSegment.
   **/
  bool is_neighbor_ = false;

  std::string id_;

 public:
  std::vector<LaneSegment> lane_segments;  ///< lane segments of the route
};

}  // namespace t2::planning::route_lane_manager
