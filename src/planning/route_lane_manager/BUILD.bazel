load("@rules_cc//cc:defs.bzl", "cc_library", "cc_test")

package(default_visibility = ["//visibility:public"])

PLANNING_COPTS = ["-DMODULE_NAME=\\\"planning\\\""]

cc_library(
    name = "path",
    srcs = ["path.cpp"],
    hdrs = ["path.hpp"],
    copts = PLANNING_COPTS,
    deps = [
        "//src/common/core",  # T2_*
        "//src/common/math",
        "//src/common/status",
        "//src/interfaces/planning_trajectory_msgs",
        "//src/map/hdmap",
        "//src/map/hdmap:hdmap_util",
        "//src/map/proto:map_lane_cc_proto",
        "//src/planning:planning_module_exception",  # PlanningModuleException
    ],
)

cc_test(
    name = "path_test",
    size = "small",
    srcs = ["path_test.cpp"],
    deps = [
        "path",
        "//src/planning/proto:routing_cc_proto",
        "@googletest//:gtest_main",
    ],
)

cc_library(
    name = "route_segments",
    srcs = ["route_segments.cpp"],
    hdrs = ["route_segments.hpp"],
    copts = PLANNING_COPTS,
    deps = [
        "path",
        "//src/interfaces/planning_msgs",  # inter-module planning messages
        "//src/planning/proto:routing_cc_proto",
    ],
)

cc_library(
    name = "route_lane_manager",
    copts = PLANNING_COPTS,
    deps = [
        "path",
        "route_segments",
    ],
)
