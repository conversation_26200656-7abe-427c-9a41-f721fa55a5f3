// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "src/common/core/logging.hpp"          // T2_*
#include "src/planning/planning_component.hpp"  // PlanningComponent

namespace t2::planning {

void PlanningComponent::SetupCommunication() {
  /* ===== Readers =====*/
  localization_reader_ = node_.create_polling_subscription<LocalizationEstimate>(
      kLocalizationPoseTopic,
      rclcpp::DefaultQoS().keep_last(1));  ///< keep only the last LocalizationEstimate

  // perception_obstacles_reader_ = node_.create_polling_subscription<PerceptionObstacles>(
  //     kPerceptionObstacleTopic, rclcpp::DefaultQoS());

  prediction_obstacles_reader_ = node_.create_polling_subscription<PredictionObstacles>(
      kPredictionTopic, rclcpp::DefaultQoS());

  chassis_reader_ = node_.create_polling_subscription<Chassis>(kChassisTopic, rclcpp::DefaultQoS());

  planning_request_reader_ = node_.create_polling_subscription<PlanningRequest>(
      kPlanningRequestTopic, rclcpp::DefaultQoS());

  update_parameter_reader_ = node_.create_polling_subscription<UpdateParameter>(
      kUpdateParameterTopic, rclcpp::DefaultQoS());

  /* ===== Writers =====*/
  trajectory_writer_ = node_.create_publisher<ADCTrajectory>(
      kPlanningTopic,
      rclcpp::DefaultQoS{}
          .keep_last(100)
          .resource_limits_max_non_self_contained_type_serialized_size(500 * 1024  // 500 KiB
                                                                       ));

  T2_PLAN_CHECK(localization_reader_ && localization_reader_.get());
  T2_PLAN_CHECK(prediction_obstacles_reader_ && prediction_obstacles_reader_.get());
  T2_PLAN_CHECK(chassis_reader_ && chassis_reader_.get());
  T2_PLAN_CHECK(planning_request_reader_ && planning_request_reader_.get());
  T2_PLAN_CHECK(update_parameter_reader_ && update_parameter_reader_.get());
  T2_PLAN_CHECK(trajectory_writer_ && trajectory_writer_.get());

  T2_INFO << "PlanningComponent is successfully initialized";
}

::apex::executor::subscription_list PlanningComponent::get_triggering_subscriptions_impl() const {
  return {prediction_obstacles_reader_};
}

::apex::executor::subscription_list PlanningComponent::get_non_triggering_subscriptions_impl()
    const {
  return {chassis_reader_, localization_reader_};
}

::apex::executor::publisher_list PlanningComponent::get_publishers_impl() const {
  return {trajectory_writer_};
}

}  // namespace t2::planning
