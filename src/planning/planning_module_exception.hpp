// Copyright (c) 2025 T2 Inc. All rights reserved.
#pragma once

#include <exception>
#include <iostream>
#include <sstream>
#include <string>

#include <common_msgs/msg/health.hpp>  // Health

#include "src/common/core/logging.hpp"                         // T2_*
#include "src/planning/proto/planning_module_error_code.pb.h"  // ::t2::planning::PlanningModuleErrorCode

#define T2_PLAN_CHECK(CONDITION)                                                                  \
  ((CONDITION) ? ::t2::planning::ErrorStream() : ::t2::planning::ErrorStream(__FILE__, __LINE__)) \
      << #CONDITION ", "

#define T2_PLAN_CHECK_FULL(ERROR_CODE, CONDITION)                             \
  ((CONDITION) ? ::t2::planning::ErrorStream()                                \
               : ::t2::planning::ErrorStream(__FILE__, __LINE__, ERROR_CODE)) \
      << #CONDITION ", "

// Full version with all parameters
#define T2_PLAN_CHECK_PRETTY_FULL(CONDITION, ERROR_FLAGS, WARN_FLAGS, RESET)                      \
  ((CONDITION) ? ::t2::planning::ErrorStream()                                                    \
               : ::t2::planning::ErrorStream(__FILE__, __LINE__, ERROR_FLAGS, WARN_FLAGS, RESET)) \
      << #CONDITION ", "

// Simpler macro that defaults RESET to true
#define T2_PLAN_CHECK_PRETTY(CONDITION, ERROR_FLAGS, WARN_FLAGS) \
  T2_PLAN_CHECK_PRETTY_FULL(CONDITION, ERROR_FLAGS, WARN_FLAGS, true)

namespace t2::planning {

using StatusFlagType = ::common_msgs::msg::StatusFlag;

// PlanningModuleException class definition
class PlanningModuleException : public std::exception {
 public:
  explicit PlanningModuleException(const std::string& message, PlanningModuleErrorCode error_code)
      : message_(message), error_code_(error_code) {}

  PlanningModuleException(const std::string& message, std::vector<StatusFlagType> error_flags_in,
                          std::vector<StatusFlagType> warn_flags_in, const bool reset_in)
      : message_(message),
        error_flags(std::move(error_flags_in)),
        warn_flags(std::move(warn_flags_in)),
        reset(reset_in) {}

  virtual const char* what() const noexcept override { return message_.c_str(); }

 private:
  std::string message_;
  PlanningModuleErrorCode error_code_ = PlanningModuleErrorCode::UNSPECIFIED;

 public:
  // For PrettyHealthInfo
  std::vector<StatusFlagType> error_flags;  ///< error flags
  std::vector<StatusFlagType> warn_flags;   ///< warning flags
  bool reset = true;                        ///< whether to reset cached members from the last cycle
};

// ErrorStream class definition
class ErrorStream {
 public:
  ErrorStream() = default;

  ErrorStream(const char* file, int line,
              PlanningModuleErrorCode error_code = PlanningModuleErrorCode::UNSPECIFIED)
      : condition_(false), error_code_(error_code) {
    std::lock_guard<std::mutex> lock(s_mutex);
    s_ss.str("");
    s_ss.clear();
    s_ss << "[Planning Exception: " << PlanningModuleErrorCode_Name(error_code) << "] (" << file
         << ":" << line << ") ";
  }

  ErrorStream(const char* file, int line, std::vector<StatusFlagType> error_flags,
              std::vector<StatusFlagType> warn_flags, const bool reset)
      : condition_(false),
        error_flags_(std::move(error_flags)),
        warn_flags_(std::move(warn_flags)),
        reset_(reset) {
    std::lock_guard<std::mutex> lock(s_mutex);
    s_ss.str("");
    s_ss.clear();
    s_ss << "[Planning Exception] ";
    s_ss << "ERROR_FLAGS = [";
    for (auto flag : error_flags_) {
      // s_ss << StatusFlag_Name(flag) << ", ";
      s_ss << static_cast<int>(flag) << ", ";
    }
    s_ss << "], ";
    s_ss << "WARN_FLAGS = [";
    for (auto flag : warn_flags_) {
      // s_ss << StatusFlag_Name(flag) << ", ";
      s_ss << static_cast<int>(flag) << ", ";
    }
    s_ss << "], ";
    s_ss << std::boolalpha << "reset=" << reset << ", ";
    s_ss << "(" << file << ":" << line << ") ";
  }

  template <typename T>
  ErrorStream& operator<<(const T& value) {
    if (!condition_) {
      std::lock_guard<std::mutex> lock(s_mutex);
      s_ss << value;
    }
    return *this;
  }

  ~ErrorStream() noexcept(false) {
    if (!condition_) {
      std::lock_guard<std::mutex> lock(s_mutex);
      const auto n_exceptions = std::uncaught_exceptions();
      if (n_exceptions == 0) {
        if (error_flags_.size() || warn_flags_.size()) {
          // new, using PrettyHealth
          throw PlanningModuleException(s_ss.str(), error_flags_, warn_flags_, reset_);
        } else {
          // original
          throw PlanningModuleException(s_ss.str(), error_code_);
        }

      } else {
        T2_FATAL << "Number of exceptions thrown: " << n_exceptions << ". Error:" << s_ss.str();
      }
    }
  }

 private:
  bool condition_ = true;
  PlanningModuleErrorCode error_code_ = PlanningModuleErrorCode::UNSPECIFIED;

  // For PrettyHealthInfo
  std::vector<StatusFlagType> error_flags_{
      /*t2::common::STATUS_FLAG_PLANNING_UNKNOWN*/};  ///< all original
                                                      ///< T2_PLAN_CHECK failures
                                                      ///< will become unknown
                                                      ///< planning error for now
  std::vector<StatusFlagType> warn_flags_;
  bool reset_ = true;  ///< reset cached results from previous cycle

  static std::ostringstream s_ss;
  static std::mutex s_mutex;  ///< to ensure thread safe
};

}  // namespace t2::planning
