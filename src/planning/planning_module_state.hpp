// Copyright (c) 2025 T2 Inc. All rights reserved.
/**
 * @file planning_module_state.h
 *
 * @brief
 */
#pragma once

#include <list>

#include <planning_msgs/msg/planning_module_status.hpp>       // PlanningModuleStatus
#include <planning_trajectory_msgs/msg/trajectory_point.hpp>  // TrajectoryPoint

#include "src/planning/common/intention_task_data.hpp"  // IntentionTaskData
#include "src/planning/config/planning_config.hpp"      // PlanningConfiguration
#include "src/planning/reference_line/reference_line.hpp"  // ReferenceLineAndRouteSegments, ReferenceLineMap

namespace t2::planning {

using TrajectoryPoint = planning_trajectory_msgs::msg::TrajectoryPoint;

/**
 * @brief Updates PlanningModuleStatus based on whether it's LANE_FOLLOW,
 * LANE_CHANGE, or LANE_CHANGE_CANCEL.
 */
void UpdatePlanningModuleState(
    ::planning_msgs::msg::PlanningModuleStatus& planning_module_status,
    const PlanningConfiguration& planning_config, const IntentionTaskData& intention,
    const std::list<ReferenceLine>& reference_lines, const TrajectoryPoint& planning_start_point,
    const ReferenceLineAndRouteSegments& lc_source_lane,
    const std::optional<ReferenceLineAndRouteSegments>& opt_lc_destination_lane,
    const ReferenceLineMap& reference_line_map);

}  // namespace t2::planning
