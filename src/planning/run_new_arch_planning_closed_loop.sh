#!/bin/bash

# DOCKER_ID=$(docker ps | grep minimal_docker | awk '{print $1}')

# Usage:
#   docker exec -u $USER desktop bash -c "cd /home/<USER>/Yatagarasu/src/planning; ./run_new_arch_planning_closed_loop.sh"

# bazel run //src/planning/integration_tests:test_planning_online_mapping

IFS=$'\n'

# Function to monitor the log file for a specific string
monitor_log_for_string() {
  local log_file="$1"
  local search_string="$2"

  echo "Monitoring $log_file for the occurrence of '$search_string'..."

  while true; do
    if [[ -f "$log_file" ]]; then
      # Check if the search string exists in the log file
      if grep -q "$search_string" "$log_file"; then
        echo "Detected '$search_string' in $log_file. Killing Apex processes."
        break
      fi
    else
      echo "Log file $log_file does not exist. Retrying..."
    fi
    sleep 5
  done
}

SCRIPT_PATH=$(cd "$(dirname "${BASH_SOURCE[0]:-$0}")" && pwd)
YATAGARASU_GIT_ROOT=$(realpath "$SCRIPT_PATH/../..")

echo "SCRIPT_PATH=$SCRIPT_PATH"
echo "YATAGARASU_GIT_ROOT=$YATAGARASU_GIT_ROOT"
cd $SCRIPT_PATH || true

rm -rf /home/<USER>/data
mkdir /home/<USER>/data

rm -rf /tmp/apex_ida_resource_creator_uds.apex

if [ "$#" -gt 1 ]; then
    scenario_file=$1
else
    scenario_file="src/planning/new_arch_scenarios/ACC/follow_ego_80_x.json"
fi

if [ ! -e "$YATAGARASU_GIT_ROOT/$scenario_file" ]; then
  echo "Error: $scenario_file does not exist"
  exit 1
fi

# echo "scenario_file=$scenario_file"

sed -i "s|\"planning_test_input_filename\": \".*\"|\"planning_test_input_filename\": \"$scenario_file\"|" integration_tests/planning_test_input_filename.json


## RUN STUFF
# Call SILS
echo ""
echo "BEGIN testing scenario_file=$scenario_file";

wall_start_time=$(date +%s%N)  # Get start time in nanoseconds

log_file="test_planning_online_mapping.log"
nohup bazel run //src/planning/integration_tests:test_planning_online_mapping > $log_file 2>&1 &

search_string="Error: Process 'planning_online_mapping'"
monitor_log_for_string "$log_file" "$search_string"

wall_end_time=$(date +%s%N)    # Get end time in nanoseconds
elapsed_time=$(( (wall_end_time - wall_start_time) / 1000000000 ))  # Convert to seconds
echo "Elapsed time: $elapsed_time seconds"
echo "  END testing $scenario_file";
echo ""

# Kill Apex processes
# shellcheck disable=SC2009
for i in $(ps aux | grep planning_online_mapping); do pid=$(echo $i | awk '{print $2}'); echo "kill $pid"; kill $pid; done
