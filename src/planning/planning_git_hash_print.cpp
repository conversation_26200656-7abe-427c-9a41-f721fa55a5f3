// Copyright (c) 2025 T2 Inc. All rights reserved.
#include "src/planning/planning_git_hash_print.hpp"  // Get*

#include "src/planning/planning_git_hash.hpp"  // macros

namespace t2::planning {

std::string_view GetYatagarasuGitHash() { return YATAGARASU_GIT_HASH; }

std::string_view GetYatagarasuGitBranch() { return YATAGARASU_GIT_BRANCH; }

/*
std::string_view GetPlanningGitHash() { return PLANNING_GIT_HASH; }

std::string_view GetPlanningGitBranch() { return PLANNING_GIT_BRANCH; }

std::string_view GetMapGitHash() { return MAP_GIT_HASH; }

std::string_view GetMapGitBranch() { return MAP_GIT_BRANCH; }
*/

}  // namespace t2::planning
