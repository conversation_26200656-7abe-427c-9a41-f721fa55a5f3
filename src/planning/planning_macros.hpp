// Copyright (c) 2025 T2 Inc. All rights reserved.
#pragma once

#define REGISTER_INTER_PLANNING_MSG(X) using X = ::planning_msgs::msg::X

#define REGISTER_INTER_TRAJECTORY_MSG(X) using X = ::planning_trajectory_msgs::msg::X

#define REGISTER_INTER_PERCEPTION_MSG(X) using X = ::perception_msgs::msg::X
#define REGISTER_INTER_PREDICTION_MSG(X) using X = ::prediction_msgs::msg::X
#define REGISTER_INTER_LOCALIZATION_MSG(X) using X = ::localization_msgs::msg::X
#define REGISTER_INTER_CHASSIS_MSG(X) using X = ::canbus_msgs::msg::X
#define REGISTER_INTER_COMMON_MSG(X) using X = ::common_msgs::msg::X

#define REGISTER_GEOMETRY_COMMON_MSG(X) using X = ::geometry_msgs::msg::X
