// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "src/planning/select_trajectory/select_trajectory.hpp"

#include <canbus_msgs/msg/chassis.hpp>  // Chassis

#include "src/common/config/vehicle_config_helper.hpp"             // VehicleConfigHelper
#include "src/planning/common/intention_task_data.hpp"             // IntentionTaskDataLogic
#include "src/planning/common/trajectory/trajectory.hpp"           // PrependTrajectoryPoints
#include "src/planning/planner/path_planner/path_planner_nlp.hpp"  // diff

namespace t2::planning {

using GearPosition = ::canbus_msgs::msg::GearPosition;

// rclcpp::Node& node
// rclcpp::Clock::SharedPtr clock
using ::t2::common::config::VehicleConfigHelper;

void SelectTrajectory::Select(
    ADCTrajectory& output_trajectory,
    std::shared_ptr<IntentionTaskData>& last_trajectory_intention_ptr,
    PlanningTrajectory& prev_trajectory, const ReferenceLineMap& reference_line_map,
    const std::list<IntentionTaskData>& list_intention_task_data, const double current_time_stamp,
    const std::vector<TrajectoryPoint>& stitching_trajectory, const std::string replan_reason,
    const double start_timestamp, const PlannerConfig& planner_config,
    const PlanningConfiguration& planning_config, const LocalizationInfo& localization_info,
    const std::list<ReferenceLine>& list_reference_lines, const rclcpp::Clock::SharedPtr& clock) {
  const bool has_drivable =
      std::end(list_intention_task_data) !=
      std::find_if(
          std::begin(list_intention_task_data), std::end(list_intention_task_data),
          [&](const auto& intention_task_data) { return intention_task_data.is_drivable; });
  if (!has_drivable) {
    const std::string msg = "planner failed to make a driving plan";
    T2_ERROR << msg;
    if (!prev_trajectory.trajectory.empty()) {
      prev_trajectory.trajectory.clear();
    }
    last_trajectory_intention_ptr.reset();
    return;
  }

  /*
  best_intention_task_data is drivable, and has the lowest cost and "tie
  break cost" among all intention task data
  */
  T2_PLAN_CHECK(!list_intention_task_data.empty()) << "failed to generate intention task data";
  std::reference_wrapper<const IntentionTaskData> ref_best_intention_task_data =
      list_intention_task_data.front();

  ref_best_intention_task_data = list_intention_task_data.front();

  const auto& best_intention_task_data = ref_best_intention_task_data.get();

  std::reference_wrapper<const IntentionTaskData> ref_intention_task_data =
      list_intention_task_data.front();
  for (const auto& intention_task_data : list_intention_task_data) {
    ref_intention_task_data = intention_task_data;
    if (!intention_task_data.lanes.is_on_segment) {
      break;
    }
  }

  last_trajectory_intention_ptr = std::make_shared<IntentionTaskData>(best_intention_task_data);

  for (const auto& lane_seg : best_intention_task_data.lanes.lane_segments) {
    output_trajectory.lane_id.push_back(lane_seg.lane->id().id());
  }

  for (const auto& lane_seg : ref_intention_task_data.get().lanes.lane_segments) {
    output_trajectory.target_lane_id.push_back(lane_seg.lane->id().id());
  }

  output_trajectory.trajectory_type = best_intention_task_data.trajectory_type;

  const auto& reference_line =
      GetSuitableReferenceLine(best_intention_task_data.is_lane_change, reference_line_map);

  output_trajectory.foxbox_z = localization_info.z;
  auto& lane_change_data = output_trajectory.lane_change_data;

  // We set output_trajectory equal to the discretized_trajectory which
  // has a Sampling Period of planner_config.dt
  auto& only_trajectory = output_trajectory.only_trajectory;
  const std::vector<TrajectoryPoint>& discretized_trajectory =
      best_intention_task_data.discretized_trajectory;

  only_trajectory = {discretized_trajectory.begin(), discretized_trajectory.end()};

  prev_trajectory.trajectory = discretized_trajectory;
  prev_trajectory.header_time = current_time_stamp;

  trajectory::PrependTrajectoryPoints(
      std::vector<TrajectoryPoint>(stitching_trajectory.begin(), stitching_trajectory.end() - 1),
      prev_trajectory.trajectory);
  trajectory::PopulateTrajectoryProtobuf(output_trajectory, prev_trajectory, clock);

  const auto& trajectory_planner_config = planner_config.trajectory_planner_config;
  if (trajectory_planner_config.enable_export_ipopt_results) {
    LogTrajectories(start_timestamp, best_intention_task_data, prev_trajectory.trajectory, 0.0, 0.0,
                    planner_config);
  }

  if (planning_config.lane_change_config.enable_export_lon_lat_data) {
    // collect longitudinal, lateral s, v, a
    LogLongitudinalAndLateralData(lane_change_data, best_intention_task_data,
                                  discretized_trajectory, list_reference_lines, localization_info,
                                  opt_previous_localization_info, planner_config);
  }
  // we store the previous localization estimate
  opt_previous_localization_info = localization_info;

  bool is_on_reference_line = reference_line.IsOnLane(best_intention_task_data.adc_sl_boundary);
  IntentionTaskDataLogic::ExportEngageAdvice(
      output_trajectory.debug.engage_advice, reference_line, best_intention_task_data.lanes,
      best_intention_task_data.adc_sl_boundary, best_intention_task_data.aligned_vehicle_state,
      is_on_reference_line, best_intention_task_data.is_drivable);

  // update output_trajectory
  output_trajectory.is_replan = stitching_trajectory.size() == 1;
  if (output_trajectory.is_replan) {
    T2_WARN << "Stitching trajectory failed reason: " << replan_reason;
  }

  output_trajectory.gear = GearPosition::GEAR_DRIVE;

  output_trajectory.debug.planning_module_status = best_intention_task_data.planning_module_status;

  const double now_in_seconds = clock->now().seconds();
  output_trajectory.header.creation_timestamp = start_timestamp;
  output_trajectory.header.creation_timestamp = now_in_seconds;
}

void LogTrajectories(double start_timestamp, const IntentionTaskData& intention_task_data,
                     const std::vector<TrajectoryPoint>& prev_trajectory,
                     double front_vehicle_distance, double front_vehicle_speed,
                     const PlannerConfig& planner_config) {
  std::stringstream ss;
  ss << std::fixed << std::setprecision(8);
  ss << "timestamp:" << start_timestamp << "\n";

  std::vector<double> speed_s, speed_t, speed_v, speed_a, speed_j;
  const SVAProfile& sva_profile = intention_task_data.sva_profile;
  const size_t n_points = sva_profile.size();
  T2_PLAN_CHECK(n_points) << "Empty sva_profile";
  auto s = sva_profile.s_profile, v = sva_profile.v_profile, a = sva_profile.a_profile;
  double t = 0;
  const double dt = planner_config.dt;  // hardcoded
  for (size_t i = 0; i < n_points - 1; ++i) {
    speed_s.push_back(*s++);
    speed_t.push_back(t);
    speed_v.push_back(*v++);
    const double curr_a = *a;
    speed_a.push_back(*a++);
    const double next_a = *a;
    speed_j.push_back((next_a - curr_a) / dt);
    t += dt;
  }
  {
    speed_s.push_back(*s);
    speed_t.push_back(t);
    speed_v.push_back(*v);
    speed_a.push_back(*a);
    speed_j.push_back(speed_j.empty() ? 0 : speed_j.back());
  }
  ss << "speed_s,";
  std::copy(speed_s.begin(), speed_s.end(), std::ostream_iterator<double>(ss, ","));
  ss << "\n";
  ss << "speed_t,";
  std::copy(speed_t.begin(), speed_t.end(), std::ostream_iterator<double>(ss, ","));
  ss << "\n";
  ss << "speed_v,";
  std::copy(speed_v.begin(), speed_v.end(), std::ostream_iterator<double>(ss, ","));
  ss << "\n";
  ss << "speed_a,";
  std::copy(speed_a.begin(), speed_a.end(), std::ostream_iterator<double>(ss, ","));
  ss << "\n";
  ss << "speed_j,";
  std::copy(speed_j.begin(), speed_j.end(), std::ostream_iterator<double>(ss, ","));
  ss << "\n";

  // auto& discretized_trajectory = intention_task_data.discretized_trajectory;
  std::vector<double> discr_s, discr_t, discr_v, discr_a, discr_j;
  for (const TrajectoryPoint& p : prev_trajectory) {
    discr_v.push_back(p.v);
    discr_a.push_back(p.a);
    discr_t.push_back(p.relative_time);
    discr_j.push_back(p.da);
  }
  ss << "discr_v,";
  std::copy(discr_v.begin(), discr_v.end(), std::ostream_iterator<double>(ss, ","));
  ss << "\n";
  ss << "discr_a,";
  std::copy(discr_a.begin(), discr_a.end(), std::ostream_iterator<double>(ss, ","));
  ss << "\n";
  ss << "discr_t,";
  std::copy(discr_t.begin(), discr_t.end(), std::ostream_iterator<double>(ss, ","));
  ss << "\n";
  ss << "discr_j,";
  std::copy(discr_j.begin(), discr_j.end(), std::ostream_iterator<double>(ss, ","));
  ss << "\n";

  // front vehicle
  ss << "front,";
  ss << front_vehicle_distance << "," << front_vehicle_speed;
  ss << "\n";

  T2_INFO << "LogTrajectories"
          << "\n"
          << ss.str();
}

void LogLongitudinalAndLateralData(
    LaneChangeData& lane_change_data, const IntentionTaskData& intention_task_data,
    const std::vector<TrajectoryPoint>& discretized_trajectory,
    const std::list<ReferenceLine>& list_reference_lines, const LocalizationInfo& localization_info,
    const std::optional<LocalizationInfo>& opt_previous_localization_info,
    const PlannerConfig& planner_config) {
  // const auto& target_lane_reference_line = list_reference_lines.front();
  // const auto& ego_lane_reference_line = list_reference_lines.back();

  // use the original lane's reference line in both lane-change and lane-keep
  const auto& reference_line = list_reference_lines.back();

  T2_INFO << "LogLongitudinalAndLateralData: Select intention \"" << intention_task_data.name
          << "\"";

  std::vector<double> headings, tan_headings;  // of size n-1
  std::vector<double> vs, as, ts;              // of size n

  const size_t num_trajectory_points = discretized_trajectory.size();

  IpoptResult& lon_data = lane_change_data.lon_data;
  IpoptResult& lat_data = lane_change_data.lat_data;
  IpoptResult& heading_data = lane_change_data.heading_data;

  for (size_t i = 0; i < num_trajectory_points - 1; ++i) {
    // current point (index i+1)
    const TrajectoryPoint& curr_trajectory_point = discretized_trajectory[i + 1];
    const double curr_t = curr_trajectory_point.relative_time;
    const auto& curr_path_point = curr_trajectory_point.path_point;
    const double curr_x = curr_path_point.x;
    const double curr_y = curr_path_point.y;
    const common::math::Vec2d curr_xy(curr_x, curr_y);
    const auto opt_curr_sl_point = reference_line.XYToSL(curr_xy);
    T2_PLAN_CHECK(opt_curr_sl_point)
        << "Cannot find SLpoint for curr_x=" << curr_x << ", curr_y=" << curr_y;
    const auto& curr_sl_point = *opt_curr_sl_point;
    const double curr_s = curr_sl_point.s;
    const double curr_l = curr_sl_point.l;

    // previous point (index i)
    const TrajectoryPoint& prev_trajectory_point = discretized_trajectory[i];
    const double prev_t = prev_trajectory_point.relative_time;
    const auto& prev_path_point = prev_trajectory_point.path_point;
    const double prev_x = prev_path_point.x;
    const double prev_y = prev_path_point.y;
    const common::math::Vec2d prev_xy(prev_x, prev_y);
    const auto opt_prev_sl_point = reference_line.XYToSL(prev_xy);
    T2_PLAN_CHECK(opt_prev_sl_point)
        << "Cannot find SLpoint for prev_x=" << prev_x << ", prev_y=" << prev_y;
    const auto& prev_sl_point = *opt_prev_sl_point;
    const double prev_s = prev_sl_point.s;
    const double prev_l = prev_sl_point.l;
    const double prev_v = prev_trajectory_point.v;

    ts.push_back(prev_t);
    vs.push_back(prev_v);
    as.push_back(prev_trajectory_point.a);

    const double tan_heading = (curr_l - prev_l) / (curr_s - prev_s);  ///< tangent of heading
    const double heading = atan(tan_heading);
    tan_headings.push_back(tan_heading);
    headings.push_back(heading);
    heading_data.s.push_back(heading);

    const double delta_t = curr_t - prev_t;  // dt from relative time
    // delta_ts.push_back(delta_t);

    lon_data.s.push_back(prev_s);  // longitudinal position
    const double lon_v = (curr_s - prev_s) / delta_t;
    lon_data.v.push_back(lon_v);  // longitudinal velocity

    lat_data.s.push_back(prev_l);  // lateral position
    const double lat_v = (curr_l - prev_l) / delta_t;
    lat_data.v.push_back(lat_v);  // lateral velocity

    // CORNERS PLANNED LATERAL POSITION
    // corners planned lateral position
    CornersLateralPosition& corners_lateral_position = lane_change_data.corners_lateral_position;

    PlannedCornersPosition& planned_corners_position =
        corners_lateral_position.planned_corners_position;

    // apollo/modules/common/data/vehicle_param.pb.txt
    const auto& vehicle_config = VehicleConfigHelper::GetInstance().GetConfig();

    const double front_edge_to_center =
        vehicle_config.vehicle_param().front_edge_to_center();                                // [m]
    const double back_edge_to_center = vehicle_config.vehicle_param().back_edge_to_center();  // [m]
    const double left_edge_to_center = vehicle_config.vehicle_param().left_edge_to_center();  // [m]
    const double right_edge_to_center =
        vehicle_config.vehicle_param().right_edge_to_center();  // [m]

    // front planned corners position
    planned_corners_position.front_right_corner_l.push_back(
        prev_l - right_edge_to_center * cos(heading) + front_edge_to_center * sin(heading));
    planned_corners_position.front_left_corner_l.push_back(
        prev_l + left_edge_to_center * cos(heading) + front_edge_to_center * sin(heading));

    // rear planned corners position
    planned_corners_position.rear_right_corner_l.push_back(
        prev_l - right_edge_to_center * cos(heading) + back_edge_to_center * sin(heading));
    planned_corners_position.rear_left_corner_l.push_back(
        prev_l + left_edge_to_center * cos(heading) + back_edge_to_center * sin(heading));

    // // front planned corners position
    // planned_corners_position.add_front_right_corner_l(
    //     prev_l - vehicle_param.right_edge_to_center() * cos(heading) +
    //     vehicle_param.front_edge_to_center() * sin(heading));
    // planned_corners_position.add_front_left_corner_l(
    //     prev_l + vehicle_param.left_edge_to_center() * cos(heading) +
    //     vehicle_param.front_edge_to_center() * sin(heading));

    // // rear planned corners position
    // planned_corners_position.add_rear_right_corner_l(
    //     prev_l - vehicle_param.right_edge_to_center() * cos(heading) +
    //     vehicle_param.back_edge_to_center() * sin(heading));
    // planned_corners_position.add_rear_left_corner_l(
    //     prev_l + vehicle_param.left_edge_to_center() * cos(heading) +
    //     vehicle_param.back_edge_to_center() * sin(heading));

    // corners measured lateral position
    if (opt_previous_localization_info.has_value()) {
      // current localization
      const double curr_x_measured = localization_info.x;
      const double curr_y_measured = localization_info.y;
      const common::math::Vec2d curr_xy_measured(curr_x_measured, curr_y_measured);
      const auto opt_curr_sl_point_measured = reference_line.XYToSL(curr_xy_measured);
      const auto& curr_sl_point_measured = *opt_curr_sl_point_measured;
      const double curr_l_measured = curr_sl_point_measured.l;
      const double curr_s_measured = curr_sl_point_measured.s;

      // previous localization
      const double prev_x_measured = opt_previous_localization_info->x;
      const double prev_y_measured = opt_previous_localization_info->y;
      const common::math::Vec2d prev_xy_measured(prev_x_measured, prev_y_measured);
      const auto opt_prev_sl_point_measured = reference_line.XYToSL(prev_xy_measured);
      const auto& prev_sl_point_measured = *opt_prev_sl_point_measured;
      const double prev_l_measured = prev_sl_point_measured.l;
      const double prev_s_measured = prev_sl_point_measured.s;

      // calculate heading
      const double delta_s_measured = curr_s_measured - prev_s_measured;
      const double delta_l_measured = curr_l_measured - prev_l_measured;
      double heading_measured = atan(delta_l_measured / delta_s_measured);

      MeasuredCornersPosition& measured_corners_position =
          corners_lateral_position.measured_corners_position;

      // front measured corners position
      measured_corners_position.front_right_corner_l =
          curr_l_measured - right_edge_to_center * cos(heading_measured) +
          front_edge_to_center * sin(heading_measured);
      // curr_l_measured - vehicle_param.right_edge_to_center() * cos(heading_measured) +
      // vehicle_param.front_edge_to_center() * sin(heading_measured);
      measured_corners_position.front_left_corner_l = curr_l_measured +
                                                      left_edge_to_center * cos(heading_measured) +
                                                      front_edge_to_center * sin(heading_measured);
      // curr_l_measured + vehicle_param.left_edge_to_center() * cos(heading_measured) +
      // vehicle_param.front_edge_to_center() * sin(heading_measured);

      // rear measured corners position
      measured_corners_position.rear_right_corner_l = curr_l_measured -
                                                      right_edge_to_center * cos(heading_measured) +
                                                      back_edge_to_center * sin(heading_measured);
      // curr_l_measured - vehicle_param.right_edge_to_center() * cos(heading_measured) +
      // vehicle_param.back_edge_to_center() * sin(heading_measured);
      measured_corners_position.rear_left_corner_l = curr_l_measured +
                                                     left_edge_to_center * cos(heading_measured) +
                                                     back_edge_to_center * sin(heading_measured);
      ;
      // curr_l_measured + vehicle_param.left_edge_to_center() * cos(heading_measured) +
      // vehicle_param.back_edge_to_center() * sin(heading_measured);

      // measured lateral position and heading for debug.
      corners_lateral_position.measured_lateral_position = curr_l_measured;
      corners_lateral_position.measured_heading = heading_measured;
    }
  }

  if (!discretized_trajectory.empty()) {
    // current point
    const auto& curr_trajectory_point = discretized_trajectory.back();
    ts.push_back(curr_trajectory_point.relative_time);
    vs.push_back(curr_trajectory_point.v);
    as.push_back(curr_trajectory_point.a);
  }

  for (size_t i = 0; i < num_trajectory_points - 1; ++i) {
    const double delta_t = ts[i + 1] - ts[i];
    const double lon_a = (vs[i + 1] * cos(headings[i + 1]) - vs[i] * cos(headings[i])) / delta_t;
    const double lat_a = (vs[i + 1] * sin(headings[i + 1]) - vs[i] * sin(headings[i])) / delta_t;
    lon_data.a.push_back(lon_a);
    lat_data.a.push_back(lat_a);

    const double heading_v = (headings[i + 1] - headings[i]) / delta_t;
    heading_data.v.push_back(heading_v);
  }

  lane_change_data.lane_change_progress = intention_task_data.lane_change_progress;

  // PathPlannerNLP data
  auto& path_planner_nlp_data = lane_change_data.path_planner_nlp_data;
  auto& lane_change_information = intention_task_data.lane_change_information;
  path_planner_nlp_data.theta0 = lane_change_information.theta0;
  path_planner_nlp_data.theta0_error = lane_change_information.theta0_error;
  path_planner_nlp_data.l0 = lane_change_information.l0;
  path_planner_nlp_data.l0_error = lane_change_information.l0_error;
  path_planner_nlp_data.max_change_heading = lane_change_information.max_change_heading;
  const auto& lci_lon = lane_change_information.lon;
  std::vector<double> lci_lat = lane_change_information.lat;
  for (size_t i = 0; i < lci_lon.size(); ++i) {
    path_planner_nlp_data.lon.push_back(lci_lon[i]);
  }

  const double delta_s = planner_config.get_suitable_longitudinal_step_size(
      intention_task_data.planning_start_point.v);
  auto& lat = path_planner_nlp_data.lat;
  for (size_t i = 0; i < lci_lat.size(); ++i) {
    lat.s.push_back(lci_lat[i]);
  }
  lci_lat = diff(lci_lat);  // 1st-order diff
  for (size_t i = 0; i < lci_lat.size(); ++i) {
    lci_lat[i] /= delta_s;
    lat.v.push_back(lci_lat[i]);
  }
  lci_lat = diff(lci_lat);  // 2nd-order diff
  for (size_t i = 0; i < lci_lat.size(); ++i) {
    lci_lat[i] /= delta_s;
    lat.a.push_back(lci_lat[i]);
  }
}

}  // namespace t2::planning
