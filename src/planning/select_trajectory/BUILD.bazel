load("@rules_cc//cc:defs.bzl", "cc_library")

package(default_visibility = ["//visibility:public"])

cc_library(
    name = "select_trajectory",
    srcs = ["select_trajectory.cpp"],
    hdrs = ["select_trajectory.hpp"],
    copts = ["-DMODULE_NAME=\\\"planning\\\""],
    deps = [
        "//src/common/core",  # T2_*
        "//src/interfaces/canbus_msgs",  # canbus_msgs::msg::*
        "//src/interfaces/planning_msgs",  # planning_msgs::msg::*
        "//src/planning:planning_macros",  # REGISTER_*_PLANNING_MSG
        "//src/planning/common:input_message_info",  # LocalizationInfo
        "//src/planning/common:intention_task_data",
        "//src/planning/common/planning_vehicle_state",
        "//src/planning/common/trajectory",  # PlanningTrajectory
        "//src/planning/config:planner_config",
        "//src/planning/planner/path_planner:path_planner_nlp",
        "//src/planning/reference_line:reference_line_provider",
        "@apex//grace/ros/rclcpp/rclcpp",  # rclcpp::Node
    ],
)
