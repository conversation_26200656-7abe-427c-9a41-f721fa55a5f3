// Copyright (c) 2025 T2 Inc. All rights reserved.

#pragma once

#include <cmath>
#include <utility>

#include <common_msgs/msg/engage_advice.hpp>     // EngageAdvice, Advice
#include <planning_msgs/msg/adc_trajectory.hpp>  // ADCTrajectory

#include "src/planning/config/planner_config.hpp"   // PlannerConfig
#include "src/planning/config/planning_config.hpp"  // PlanningConfiguration
#include "src/planning/planning_macros.hpp"

namespace t2::planning {

REGISTER_INTER_PLANNING_MSG(ADCTrajectory);
REGISTER_INTER_COMMON_MSG(Advice);

std::pair<Advice, double> HandleSpeedLimitDriverTrigger(const Advice& current_advice,
                                                        const Advice& previous_advice,
                                                        const double& current_speed,
                                                        const double& limit, ADCTrajectory& output,
                                                        const PlanningConfiguration& config);

double ComputeBrakingDistance(const double v_0, const double v_f,
                              const PlannerConfig& planner_config);

}  // namespace t2::planning
