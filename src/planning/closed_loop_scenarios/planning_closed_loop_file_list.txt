# ACC
/apollo/modules/planning/.github/planning_closed_loop_input_files/planning_proc_input.json
/apollo/modules/planning/.github/planning_closed_loop_input_files/shirosato-kosoku-shukairo-inner-loop.json
/apollo/modules/planning/.github/planning_closed_loop_input_files/follow_ego_80_other_80.json
/apollo/modules/planning/.github/planning_closed_loop_input_files/follow_ego_80_other_60.json
/apollo/modules/planning/.github/planning_closed_loop_input_files/cut-in_ego_80_other_60.json
/apollo/modules/planning/.github/planning_closed_loop_input_files/cut-out_ego_80_other_60.json
/apollo/modules/planning/.github/planning_closed_loop_input_files/0-to-50.json
#
# To-do (Théo)
# /apollo/modules/planning/.github/planning_closed_loop_input_files/follow_ego_low_speed.json
# /apollo/modules/planning/.github/planning_closed_loop_input_files/stopping_80_0.json
#
# Lane Change by Driver Trigger
/apollo/modules/planning/.github/planning_closed_loop_input_files/lane-change_sogoshikenro-right-left-no-obstacle.json
/apollo/modules/planning/.github/planning_closed_loop_input_files/lane-change_sogoshikenro-left-right-no-obstacle.json
# Lane Change Cancel by Driver Trigger
/apollo/modules/planning/.github/planning_closed_loop_input_files/lane-change-cancel_sogoshikenro-left-right-no-obstacle.json
/apollo/modules/planning/.github/planning_closed_loop_input_files/lane-change-cancel_sogoshikenro-right-left-no-obstacle.json
#/apollo/modules/planning/.github/planning_closed_loop_input_files/start_outside_map.json
# Control ageo
/apollo/modules/planning/.github/planning_closed_loop_input_files/ageo_bicycle.json
