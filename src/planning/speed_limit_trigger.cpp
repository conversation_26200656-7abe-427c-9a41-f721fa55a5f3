// Copyright (c) 2025 T2 Inc. All rights reserved.
#include "src/planning/speed_limit_trigger.hpp"

namespace t2::planning {

// Speed Limit Driver Trigger
std::pair<Advice, double> HandleSpeedLimitDriverTrigger(const Advice& current_advice,
                                                        const Advice& previous_advice,
                                                        const double& current_speed,
                                                        const double& limit, ADCTrajectory& output,
                                                        const PlanningConfiguration& config) {
  auto compute_speed_limit = [](const double& speed, const std::vector<double>& boundaries,
                                const std::vector<double>& limits) {
    double result = speed;

    // Verify that the boundaries and limits are valids.
    T2_PLAN_CHECK(limits.size() == (boundaries.size() + 1));

    // Verify that boundaries[i] > boundaries[i+1]
    for (size_t i = 0; i < boundaries.size() - 1; i++) {
      T2_PLAN_CHECK(boundaries[i] > boundaries[i + 1]);
    }

    // Verify that limits[i] < limits[i+1]
    for (size_t i = 0; i < limits.size() - 1; i++) {
      T2_PLAN_CHECK(limits[i] < limits[i + 1]);
    }

    if (speed > boundaries.front()) {
      result = limits.back();
    }

    auto j = limits.size() - 2;
    for (size_t i = 1; i < boundaries.size(); i++) {
      if (speed > boundaries[i] && speed <= boundaries[i - 1]) {
        result = limits[j];
      }
      j = j - 1;
    }

    if (speed < boundaries.back()) {
      result = limits.front();
    }

    return result;
  };

  bool trigger_cmd = false;
  auto& speed_limit_driver_trigger = output.speed_limit_driver_trigger;
  const double speed = current_speed;

  // Trigger Processing (Do we trigger or not)
  // The advice was READY_TO_ENGAGE (t-1)
  if (previous_advice == Advice::READY_TO_ENGAGE) {
    // Now the advice is KEEP_ENGAGED (t)
    // We trigger the set of the speed limit.
    if (current_advice == Advice::KEEP_ENGAGED) {
      trigger_cmd = true;
    }
  } else if (previous_advice == Advice::KEEP_ENGAGED) {
    trigger_cmd = false;
  }

  double current_speed_limit = limit;
  double determined_speed_limit =
      compute_speed_limit(speed, config.speed_limit_driver_trigger_config.boundaries,
                          config.speed_limit_driver_trigger_config.limits);

  // When triggered, we update the speed_limit
  if (trigger_cmd) {
    // Update the current speed limit
    current_speed_limit = determined_speed_limit;
  }

  //(field 1)-Set trigger command
  speed_limit_driver_trigger.trigger = trigger_cmd;

  //(field 2)-Set speed_limit
  speed_limit_driver_trigger.speed_limit = current_speed_limit;

  //(field 3)-Write debug speed limit
  speed_limit_driver_trigger.debug_speed_limit = determined_speed_limit;

  // Update engage_advice and speed_limit
  return {current_advice, current_speed_limit};
};

double ComputeBrakingDistance(const double v_0, const double v_f,
                              const PlannerConfig& planner_config) {
  double a_min = planner_config.trajectory_planner_config.acc_requirement_parameters.a_min_ttc;
  double j_max = planner_config.trajectory_planner_config.bounds.j_max;
  double j_min = planner_config.trajectory_planner_config.bounds.j_min;
  double a_min_sq = a_min * a_min;
  double v = -a_min_sq / (2.0 * j_min) + 0.5 * a_min_sq / j_max - v_0 + v_f;
  return (a_min_sq / (2.0 * j_min) + v_0) *
             (-a_min_sq / (2.0 * j_min) + 0.5 * a_min_sq / j_max - v_0 + v_f) / a_min +
         0.5 * v * v / a_min;
}
}  // namespace t2::planning
