// Copyright (c) 2025 T2 Inc. All rights reserved.
#include "planning_hdmap_utils.hpp"  // LaneInfo

#include <iomanip>  // setprecision

// PlanningResponse
#include "planning_test_input.hpp"  // LaneChangeObstacle, LaneChangeDriverTriggerInfo, MotionProfile
#include "src/common/core/logging.hpp"  // T2_*

namespace t2::planning::test {

namespace hdmap = map::hdmap;

void UpdateMapFlagFile(const std::string& map_dir) {
  const std::string config = "/apollo/map_flagfile.txt";
  std::ofstream fs(config);
  fs << "--map_dir=" << map_dir << std::endl;
  if (!fs) {
    T2_ERROR << "FAILED: cannot write " << config;
  }
}

// `distance` ahead of the `in_lane_pos` position of `in_lane`,
// we derive the `out_lane` and its position `out_lane_pos` .
// Retrived from #360.
LanePosition GetLanePositionAhead(const hdmap::LaneInfoConstPtr& in_lane, const double distance,
                                  const double in_lane_pos) {
  T2_PLAN_CHECK(in_lane && distance >= 0 && in_lane_pos >= 0)
      << std::boolalpha << (in_lane != nullptr) << ", distance=" << distance
      << ", in_lane_pos=" << in_lane_pos;

  const auto hdmap = hdmap::HDMapUtil::BaseMapPtr();

  // initialize to the starting point of the input lane
  LanePosition result{in_lane, distance + in_lane_pos, 0.0};
  auto& [out_lane, out_lane_pos, _] = result;

  while (out_lane) {
    // iterating over the successors
    // make it a condition, just to avoid successor lane_info_ptr is null
    const hdmap::Lane& lane = out_lane->lane();
    const double length = lane.length();

    T2_DEBUG << "Current lane: " << lane.id().id() << ", length=" << length
             << ", remaining distance=" << out_lane_pos;

    if (out_lane_pos >= length) {
      if (lane.successor_id().empty()) {
        out_lane = nullptr;
        break;  // invalid output
      } else {
        hdmap::Id successor_id = lane.successor_id(0);
        out_lane = hdmap->GetLaneById(successor_id);
        out_lane_pos -= length;
      }
    } else {
      T2_DEBUG << "lane=" << lane.id().id() << ", lane_pos=" << out_lane_pos;
      break;  // valid output
    }
  };
  if (!out_lane) {
    out_lane_pos = std::nan("");
    T2_WARN << "Cannot find out_lane for in_lane=" << in_lane->lane().id().id()
            << ", distance=" << distance << ", in_lane_pos=" << in_lane_pos
            << "; return (null, nan)";
  }
  return result;
}

LanePosition GetLanePositionFromXYPoint(const Obstacle::TrajectoryXYPoint& xypoint) {
  return GetLanePositionFromXY(xypoint.x, xypoint.y);
}

LanePosition GetLanePositionFromXY(const double x, const double y) {
  LanePosition result;
  auto& [lane_ptr, s, l] = result;
  const auto hdmap = hdmap::HDMapUtil::BaseMapPtr();
  common::PointENU point;
  point.set_x(x);
  point.set_y(y);
  hdmap->GetNearestLane(point, &lane_ptr, &s, &l);
  return result;
}

std::tuple<double, double> GetXYFromLanePosition(const hdmap::LaneInfoConstPtr& lane_ptr,
                                                 const double s) {
  common::PointENU point = lane_ptr->GetSmoothPoint(s);
  return {point.x(), point.y()};
}

std::vector<double> GenerateVelocityProfile(double v0) { return {v0}; }

std::vector<double> GenerateVelocityProfile(const MotionProfile& profile, double v0, double dt) {
  std::vector<double> velocity_profile{v0};  // Start with v0 at t = 0

  for (const auto& phase : profile.phases) {
    double duration = phase.duration;
    double rate = phase.rate;
    double target = phase.target;

    // Infer the missing value
    if (std::isnan(duration)) {
      if (!std::isnan(rate) && !std::isnan(target)) {
        duration = (target - velocity_profile.back()) / rate;
      }
    } else if (std::isnan(rate)) {
      if (!std::isnan(duration) && !std::isnan(target)) {
        rate = (target - velocity_profile.back()) / duration;
      }
    } else if (std::isnan(target)) {
      if (!std::isnan(duration) && !std::isnan(rate)) {
        target = velocity_profile.back() + rate * duration;
      }
    }

    T2_PLAN_CHECK_FULL(planning::PlanningModuleErrorCode::INVALID_INPUT,
                       std::isfinite(duration) && std::isfinite(rate) && std::isfinite(target))
        << "duration=" << duration << ", rate=" << rate << ", curr_v=" << velocity_profile.back()
        << ", target=" << target;

    // Use time-based loop for better precision and ensure target is included
    double time_in_phase = dt;
    const double dv = rate * dt;
    while (time_in_phase < duration - 1e-8) {  // avoid floating-point precision issues
      velocity_profile.push_back(velocity_profile.back() + dv);
      time_in_phase += dt;
    }

    // Ensure final target velocity is pushed if not already there
    if (std::abs(velocity_profile.back() - target) > 1e-8) {
      velocity_profile.push_back(target);
    }
  }

  return velocity_profile;
}

std::vector<Obstacle::TrajectoryXYPoint> GenerateObstacleLaneKeepTrajectory(
    const PlannerConfig& planner_config, Obstacle::TrajectoryXYPoint xypoint,
    const std::vector<double>& velocity_profile) {
  T2_PLAN_CHECK_FULL(planning::PlanningModuleErrorCode::INVALID_INPUT, !velocity_profile.empty())
      << "Invalid velocity profile";

  auto [lane_ptr, s, _] = GetLanePositionFromXYPoint(xypoint);
  T2_DEBUG << "GenerateObstacleLaneKeepTrajectory: v=" << velocity_profile.at(0)
           << ", lane_ptr=" << lane_ptr->id().id() << ", s=" << s;

  const double dt = planner_config.dt;
  auto v_cit = velocity_profile.cbegin();
  double curr_v = *v_cit;
  const size_t n_points = planner_config.n_points;

  xypoint.t = 0.0;
  std::vector<Obstacle::TrajectoryXYPoint> trajectory(n_points, xypoint);

  double curr_t = dt;
  for (size_t i = 1; i < n_points; i++, curr_t += dt) {
    const double ds = curr_v * dt;
    std::tie(lane_ptr, s, std::ignore) = test::GetLanePositionAhead(lane_ptr, ds, s);

    auto& curr_point = trajectory[i];
    std::tie(curr_point.x, curr_point.y) = test::GetXYFromLanePosition(lane_ptr, s);
    curr_point.t = curr_t;
    curr_point.v = curr_v;

    T2_DEBUG << std::setprecision(8) << "lane_ptr=" << lane_ptr->id().id() << ", s=" << s
             << ", t=" << curr_point.t << ", x=" << curr_point.x << ", y=" << curr_point.y;

    if (v_cit != velocity_profile.cend()) {
      ++v_cit;
      curr_v = *v_cit;
    }
  }

  return trajectory;
}

hdmap::LaneInfoConstPtr GetLeftLane(const hdmap::LaneInfoConstPtr& ego_lane) {
  return ego_lane->lane().left_neighbor_forward_lane_id_size() > 0
             ? hdmap::HDMapUtil::BaseMapPtr()->GetLaneById(
                   ego_lane->lane().left_neighbor_forward_lane_id(0))
             : nullptr;
}

hdmap::LaneInfoConstPtr GetRightLane(const hdmap::LaneInfoConstPtr& ego_lane) {
  return ego_lane->lane().right_neighbor_forward_lane_id_size() > 0
             ? hdmap::HDMapUtil::BaseMapPtr()->GetLaneById(
                   ego_lane->lane().right_neighbor_forward_lane_id(0))
             : nullptr;
}

hdmap::LaneInfoConstPtr GetPredecessorLane(const hdmap::LaneInfoConstPtr& ego_lane) {
  return ego_lane->lane().predecessor_id_size() > 0
             ? hdmap::HDMapUtil::BaseMapPtr()->GetLaneById(ego_lane->lane().predecessor_id(0))
             : nullptr;
}

std::vector<Obstacle::TrajectoryXYPoint> GenerateObstacleLaneChangeTrajectory(
    const PlannerConfig& planner_config, Obstacle::TrajectoryXYPoint xypoint,
    const std::vector<double>& velocity_profile, const std::string& direction) {
  T2_PLAN_CHECK_FULL(planning::PlanningModuleErrorCode::INVALID_INPUT, !velocity_profile.empty())
      << "Invalid velocity profile";

  auto [source_lane, source_s, _] = GetLanePositionFromXYPoint(xypoint);
  auto left_lane = GetLeftLane(source_lane);
  auto right_lane = GetRightLane(source_lane);

  hdmap::LaneInfoConstPtr target_lane;
  if (direction == "auto") {
    T2_PLAN_CHECK_FULL(planning::PlanningModuleErrorCode::INVALID_INPUT, left_lane || right_lane)
        << "Invalid auto lane change instruction";
    if (left_lane) {
      target_lane = left_lane;
      T2_INFO << "Auto lane change to left";
    } else {
      target_lane = right_lane;
      T2_INFO << "Auto lane change to right";
    }
  } else if (direction == "left") {
    T2_PLAN_CHECK_FULL(planning::PlanningModuleErrorCode::INVALID_INPUT, left_lane)
        << "Invalid left lane change instruction";
    target_lane = left_lane;
  } else if (direction == "right") {
    T2_PLAN_CHECK_FULL(planning::PlanningModuleErrorCode::INVALID_INPUT, right_lane)
        << "Invalid right lane change instruction";
    target_lane = right_lane;
  } else {
    T2_PLAN_CHECK_FULL(planning::PlanningModuleErrorCode::INVALID_INPUT, false)
        << "Invalid direction=" << direction;
  }

  const double lane_change_duration = 5.0;
  const double dt = planner_config.dt;
  auto v_cit = velocity_profile.cbegin();
  double curr_v = *v_cit;
  const size_t n_points = planner_config.n_points + lane_change_duration / dt;

  xypoint.t = 0.0;
  std::vector<Obstacle::TrajectoryXYPoint> trajectory(n_points, xypoint);

  double curr_t = dt;
  double target_s = source_s;

  for (size_t i = 1; i < n_points; i++, curr_t += dt) {
    const double ds = curr_v * dt;  // constant speed for now
    std::tie(source_lane, source_s, std::ignore) =
        test::GetLanePositionAhead(source_lane, ds, source_s);

    std::tie(target_lane, target_s, std::ignore) =
        test::GetLanePositionAhead(target_lane, ds, target_s);

    auto& curr_point = trajectory[i];

    Obstacle::TrajectoryXYPoint source_point, target_point;

    std::tie(source_point.x, source_point.y) = test::GetXYFromLanePosition(source_lane, source_s);
    std::tie(target_point.x, target_point.y) = test::GetXYFromLanePosition(target_lane, target_s);

    double lc_progress = std::min(curr_t / lane_change_duration, 1.0);

    curr_point.x = source_point.x * (1 - lc_progress) + target_point.x * lc_progress;
    curr_point.y = source_point.y * (1 - lc_progress) + target_point.y * lc_progress;
    curr_point.t = curr_t;
    curr_point.v = curr_v;

    T2_DEBUG << std::setprecision(8) << "source_lane=" << source_lane->id().id()
             << ", source_s=" << source_s << ", target_lane=" << target_lane->id().id()
             << ", target_s=" << target_s << ", t=" << curr_point.t << ", x=" << curr_point.x
             << ", y=" << curr_point.y << ", curr_v=" << curr_v;

    if (v_cit != velocity_profile.cend()) {
      ++v_cit;
      curr_v = *v_cit;
    }
  }

  return trajectory;
}

std::optional<LaneChangeObstacle> GetLaneChangeObstacle(
    const PlannerConfig& planner_config,
    const std::map<double, LaneChangeObstacle>& lane_change_obstacle_map,
    const double simulation_time) {
  auto it = lane_change_obstacle_map.lower_bound(simulation_time);
  std::optional<LaneChangeObstacle> opt_lane_change_obs;
  if (it != lane_change_obstacle_map.end()) {
    auto [lane_change_time, lane_change_obs] = *it;
    if (lane_change_time - simulation_time < planner_config.dt) {
      opt_lane_change_obs = lane_change_obs;
      T2_INFO << "Found lane_change_time=" << lane_change_time << ", "
              << planning::ToJsonString(it->second);
    }
  }
  return opt_lane_change_obs;
}

LaneChangeDriverTriggerInfo GetLaneChangeDriverTriggerInfo(
    const std::map<double, LaneChangeDriverTriggerInfo>& lane_change_driver_triggers,
    const double simulation_time) {
  LaneChangeDriverTriggerInfo ret;

  for (const auto& [start_time, lane_change_info] : lane_change_driver_triggers) {
    const double end_time = start_time + lane_change_info.min_duration_sec;
    if (simulation_time >= start_time && simulation_time < end_time) {
      T2_INFO << "Found a lane_change_info: start_time=" << start_time << ", end_time=" << end_time
              << ", simulation_time=" << simulation_time;
      ret = lane_change_info;
    }
  }
  return ret;
}

PredictionObstacles ToProto(const std::map<int, Obstacle>& obstacle_map) {
  PredictionObstacles ret;
  auto& prediction_obstacles = ret.prediction_obstacles;

  // Iterate over the obstacle_map and create the corresponding proto message
  for (const auto& [_, obstacle] : obstacle_map) {
    // Create a new PredictionObstacle message
    prediction_obstacles.emplace_back();
    PredictionObstacle& prediction_obstacle = prediction_obstacles.back();

    // Set perception data
    auto& perception_obstacle = prediction_obstacle.perception_obstacle;
    perception_obstacle.id = obstacle.id;
    perception_obstacle.length = obstacle.length;
    perception_obstacle.width = obstacle.width;
    perception_obstacle.height = obstacle.height;
    perception_obstacle.theta = obstacle.theta;
    perception_obstacle.confidence = obstacle.confidence;
    // perception_obstacle.confidence_type = obstacle.confidence_type;

    // Set prediction data (timestamp)
    prediction_obstacle.timestamp = obstacle.timestamp;

    // Handle trajectories
    size_t i_traj = 0;
    bool is_position_set = false;  ///< set position as the traj point of the first traj
    for (const auto& trajectory : obstacle.trajectories) {
      // auto& prediction_trajectory = *prediction_obstacle.add_trajectory();
      prediction_obstacle.trajectory.emplace_back();
      auto& prediction_trajectory = prediction_obstacle.trajectory.back();
      for (const auto& traj_point : trajectory) {
        prediction_trajectory.trajectory_point.emplace_back();
        auto& point = prediction_trajectory.trajectory_point.back();
        point.path_point.x = traj_point.x;
        point.path_point.y = traj_point.y;
        point.relative_time = traj_point.t;
        point.v = traj_point.v;

        if (!is_position_set) {
          is_position_set = true;
          auto& position = perception_obstacle.position;
          position.x = traj_point.x;
          position.y = traj_point.y;
          position.z = 0;  ///< no z in Obstacle; only TrajectoryXYPoint
        }
      }

      if (i_traj < obstacle.trajectories_probability.size()) {
        prediction_trajectory.probability = obstacle.trajectories_probability[i_traj];
      } else {
        T2_ERROR << "Mismatch in trajectory and probability count for obstacle ID " << obstacle.id;
        prediction_trajectory.probability = 0.0;  // Set a default value if needed
      }
      ++i_traj;
    }
  }

  return ret;
}

hdmap::LaneInfoConstPtr GetLaneById(std::string_view lane_id) {
  hdmap::Id id;
  id.set_id(std::string(lane_id));
  return hdmap::HDMapUtil::BaseMapPtr()->GetLaneById(id);
}

double GetHeadingFromXY(const double x, const double y) {
  auto [lane_ptr, s, _] = GetLanePositionFromXY(x, y);
  return lane_ptr->Heading(s);
}

void AlignLocalizationWithHeading(LocalizationInfo& localization_info, const double theta) {
  constexpr double quarter_pi = M_PI * 0.25;
  localization_info.heading = theta;
  const double v = sqrt(localization_info.vx * localization_info.vx +
                        localization_info.vy * localization_info.vy);
  localization_info.vx = v * cos(theta);
  localization_info.vy = v * sin(theta);

  // quat = [w,x,y,z] = [cos((θ-pi/2)/2), 0, 0, sin((θ-pi/2)/2)]
  localization_info.qw = cos(theta * 0.5 - quarter_pi);
  localization_info.qz = sin(theta * 0.5 - quarter_pi);
  localization_info.qx = localization_info.qy = 0.0;
}

void AlignLocalizationWithHeading(LocalizationInfo& localization_info) {
  double heading = GetHeadingFromXY(localization_info.x, localization_info.y);
  AlignLocalizationWithHeading(localization_info, heading);
}

}  // namespace t2::planning::test
