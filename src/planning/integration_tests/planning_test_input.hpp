// Copyright (c) 2025 T2 Inc. All rights reserved.
#pragma once

#include <cereal/types/optional.hpp>  // optional

#include "src/planning/common/input_message_info.hpp"  // PlanningProcInput
#include "src/planning/config/planning_config.hpp"     // PlanningConfiguration
// PlanningResponse #include "src/t2common/proto/health.pb.hpp"                     //
// t2::common::PrettyHealth
#include <planning_msgs/msg/planning_request.hpp>  // PlanningRequest, PlanningCommand

#include "src/common/core/logging.hpp"  // T2_*

namespace t2::planning::test {

REGISTER_INTER_PLANNING_MSG(PlanningRequest);
REGISTER_INTER_PLANNING_MSG(PlanningCommand);

inline std::string to_lower_case_string(std::string str) {
  std::transform(str.begin(), str.end(), str.begin(),
                 [](unsigned char c) { return std::tolower(c); });
  return str;
}

inline std::string to_upper_case_string(std::string str) {
  std::transform(str.begin(), str.end(), str.begin(),
                 [](unsigned char c) { return std::toupper(c); });
  return str;
}

struct LaneChangeObstacle {
  int id = -1;
  std::string direction;

  template <typename T>
  void serialize(T& archive) {
    archive(CEREAL_NVP(id), CEREAL_NVP(direction));

    direction = to_lower_case_string(direction);

    const bool is_valid_dir = direction == "left" || direction == "right" || direction == "auto";
    if (!is_valid_dir) {
      T2_ERROR << "direction=" << direction << " is not valid";
    }
  }
};

struct StopPosition {
  bool valid = false;
  double x = 0.0;
  double y = 0.0;
  double radius = 10.0;

  template <typename T>
  void serialize(T& archive) {
    archive(CEREAL_NVP(valid), CEREAL_NVP(x), CEREAL_NVP(y), CEREAL_NVP(radius));
  }

  double dist(double x0, double y0) const { return (x - x0) * (x - x0) + (y - y0) * (y - y0); }

  bool is_close(double x0, double y0) const { return valid && (dist(x0, y0) <= radius * radius); }
};

struct MotionProfile {
  struct Phase {
    double duration = std::nan("");  ///< time [s]
    double rate = std::nan("");      ///< acceleration [m/s^2]
    double target = std::nan("");    ///< target velocity [m/s]

    template <typename T>
    void serialize(T& archive) {
      archive(CEREAL_NVP(duration), CEREAL_NVP(rate), CEREAL_NVP(target));
    }
  };

  std::vector<Phase> phases;  ///< same as in yaml
  template <typename T>
  void serialize(T& archive) {
    archive(CEREAL_NVP(phases));
  }
};  // struct MotionProfile

int status_flag_to_int(std::string_view status_str);

// /apollo/modules/t2common/proto/health.proto
struct PrettyHealthInfo {
  // See modules/t2common/proto/health.proto
  std::vector<int> warn_flags_in_int;   ///< warn_flags in integer
  std::vector<int> error_flags_in_int;  ///< error_flags in integer

  template <typename T>
  void serialize(T& archive) {
    if constexpr (T::is_loading::value) {
      T2_INFO << "Deserializing...";
      archive(cereal::make_nvp("warn_flags", warn_flags_in_int),
              cereal::make_nvp("error_flags", error_flags_in_int));

      // auto convert_to_status_flags =
      //     [](const std::vector<int>& int_flags) -> std::vector<t2::common::StatusFlag> {
      //   std::vector<t2::common::StatusFlag> status_flags;
      //   status_flags.reserve(int_flags.size());
      //   std::transform(int_flags.begin(), int_flags.end(), std::back_inserter(status_flags),
      //                  [](int status_flag_int) {
      //                    t2::common::StatusFlag status_flag =
      //                        static_cast<t2::common::StatusFlag>(status_flag_int);
      //                    T2_INFO << "status_flag=" << StatusFlag_Name(status_flag);
      //                    return status_flag;
      //                  });
      //   return status_flags;
      // };

      // warn_flags_ = convert_to_status_flags(warn_flags_in_int);
      // error_flags_ = convert_to_status_flags(error_flags_in_int);

    } else if constexpr (T::is_saving::value) {
      T2_INFO << "Serializing...";

      // auto convert_to_integers =
      //     [](const std::vector<t2::common::StatusFlag>& input_flags) -> std::vector<int> {
      //   std::vector<int> output_flags;
      //   output_flags.reserve(input_flags.size());
      //   std::transform(input_flags.begin(), input_flags.end(), std::back_inserter(output_flags),
      //                  [](const auto& status_flag) { return static_cast<int>(status_flag); });
      //   return output_flags;
      // };

      // warn_flags_in_int = convert_to_integers(warn_flags_);
      // error_flags_in_int = convert_to_integers(error_flags_);
      archive(cereal::make_nvp("warn_flags", warn_flags_in_int),
              cereal::make_nvp("error_flags", error_flags_in_int));
    }
  }

  // void set_warn_flags(std::vector<t2::common::StatusFlag> warn_flags) {
  //   warn_flags_ = std::move(warn_flags);
  // }
  // void set_error_flags(std::vector<t2::common::StatusFlag> error_flags) {
  //   error_flags_ = std::move(error_flags);
  // }
  //  const auto& warn_flags() const { return warn_flags_; }
  //  const auto& error_flags() const { return error_flags_; }

  // private:
  //  std::vector<t2::common::StatusFlag> warn_flags_;   ///< warn_flags inside PrettyHealth
  //  std::vector<t2::common::StatusFlag> error_flags_;  ///< error_flags_s inside PrettyHealth
};

/*
See
modules/tools/simulator/yaml_utils_internal.cc
modules/tools/simulator/meta_info.h
*/
struct LaneChangeDriverTriggerInfo {
  double min_duration_sec = 0.0;  ///< duration in simulation
  std::string direction;

  template <typename T>
  void serialize(T& archive) {
    archive(CEREAL_NVP(min_duration_sec), CEREAL_NVP(direction));

    direction = to_lower_case_string(direction);

    const bool is_valid_dir = direction == "left" || direction == "right" || direction == "auto";
    if (!is_valid_dir) {
      T2_ERROR << "direction=" << direction << " is not valid";
    }
  }

  PlanningRequest ToPlanningRequest(double x = std::nan(""), double y = std::nan("")) const;
};

#define ARCHIVE_OPTIONAL_NVP(NAME, VALUE)                 \
  try {                                                   \
    archive(cereal::make_nvp(NAME, VALUE));               \
  } catch (const cereal::Exception& e) {                  \
    T2_WARN << "No " #NAME " in the input: " << e.what(); \
  }

#define ARCHIVE_OPTIONAL(X) ARCHIVE_OPTIONAL_NVP(#X, X)

struct PlanningTestInput : public PlanningProcInput {
  size_t num_runs = 100;  ///< number of calls of Proc

  size_t stepsize = 1;  ///< stepsize we take to advance in the trajectory

  std::map<double, LaneChangeObstacle> lane_change_obstacle_map;  ///< key is a simulation time
  StopPosition stop_position;                                     ///< stop position
  std::string start_lane_id;                                      ///< start simulation from lane id
  std::string stop_lane_id;  ///< end simulation from lane id; will override
                             ///< stop_position
  std::map<double, LaneChangeDriverTriggerInfo>
      lane_change_driver_triggers;  ///< key is simulation time, i.e.,
                                    ///< start_after_sec

  std::map<int, MotionProfile> obstacle_motion_profile_map;  ///< velocity profile for the obstacles
  PrettyHealthInfo pretty_health_info;                       ///< default is no flags at all

  std::optional<PlanningConfiguration> opt_planning_config;  ///< planning config

  bool use_input_heading = false;  ///< use heading from scenario; default is
                                   ///< false, as we align automatically
  bool is_from_mcap = false;       ///< we extract obstacle trajectories from a mcap; if true, then
                                   ///< we do not propate obstacle trajectories for such scenarios

  bool use_cartesian_planner = false;  ///< use Théo's cartesian planner

  std::vector<int> transition_planning_module_status = {
      0};  ///<  expected planning module status transitions

  template <typename T>
  void serialize(T& archive) {
    PlanningProcInput::serialize(archive);
    archive(CEREAL_NVP(num_runs));
    ARCHIVE_OPTIONAL(stepsize);
    ARCHIVE_OPTIONAL(lane_change_obstacle_map);
    ARCHIVE_OPTIONAL(stop_position);
    ARCHIVE_OPTIONAL(start_lane_id);
    ARCHIVE_OPTIONAL(stop_lane_id);
    ARCHIVE_OPTIONAL(lane_change_driver_triggers);
    ARCHIVE_OPTIONAL(obstacle_motion_profile_map);
    ARCHIVE_OPTIONAL(pretty_health_info);
    ARCHIVE_OPTIONAL_NVP("planning_config", opt_planning_config);
    ARCHIVE_OPTIONAL(use_input_heading);
    ARCHIVE_OPTIONAL(is_from_mcap);
    ARCHIVE_OPTIONAL(use_cartesian_planner);
    ARCHIVE_OPTIONAL(transition_planning_module_status);
  }

  friend class PlanningTestSimulationComponent;

 private:
  bool use_truckmaker_ = false;  ///< set from TruckMakerConfig; if true, then
                                 ///< use Planning+Control+TruckMakerComponent
};
}  // namespace t2::planning::test
