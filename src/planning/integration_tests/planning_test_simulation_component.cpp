// Copyright (c) 2025 T2 Inc. All rights reserved.
#include "planning_test_simulation_component.hpp"  // PlanningTestSimulationComponent

#include "planning_hdmap_utils.hpp"                            // AlignLocalizationWithHeading
#include "src/common/config/map_config.hpp"                    // common::config::MapConfig
#include "src/common/runfiles/runtime_dependency_manager.hpp"  // RuntimeDependencyManager
#include "src/planning/common/obstacle.hpp"                    // Obstacle
#include "src/planning/planning_module_exception.hpp"          // PlanningModuleException
#include "src/planning/truckmaker/truckmaker_config.hpp"       // TruckMakerConfig
#include "tf2_ros/static_transform_broadcaster.h"              // StaticTransformBroadcaster

namespace t2::planning::test {

PlanningTestSimulationComponent::PlanningTestSimulationComponent(
    const std::string& planning_test_input_filename, const bool use_sils)
    : base(kPlanningTestSimulationComponentName, kPlanningTestSimulationNamespace),
      node_(get_rclcpp_node()),
      tf2_broadcaster(tf2_ros::StaticTransformBroadcaster(node_.shared_from_this())),
      use_sils_(use_sils) {
  if (use_sils) {
    /* ===== Writers =====*/
    localization_writer =
        node_.create_publisher<LocalizationEstimate>(kLocalizationPoseTopic, rclcpp::DefaultQoS());

    chassis_writer = node_.create_publisher<Chassis>(kChassisTopic, rclcpp::DefaultQoS());

    prediction_writer =
        node_.create_publisher<PredictionObstacles>(kPredictionTopic, rclcpp::DefaultQoS());

    planning_request_writer =
        node_.create_publisher<PlanningRequest>(kPlanningRequestTopic, rclcpp::DefaultQoS());

    update_parameter_writer =
        node_.create_publisher<UpdateParameter>(kUpdateParameterTopic, rclcpp::DefaultQoS());

    planning_test_writer =
        node_.create_publisher<PlanningTestMessage>(kPlanningTestTopic, rclcpp::DefaultQoS());

    // Broadcast transform to FoxgloveMapPublisherComponent
    // tf2_broadcaster = init_context.CreateTransformBroadcaster(false);
    // T2_PLAN_CHECK(tf2_broadcaster) << "Failed to CreateTransformBroadcaster";
    // transform_stamped_.source = "novatel";
    // transform_stamped_.target = "world";
    // transform_stamped_.transform.setScale(1);

    /* ===== Readers =====*/
    trajectory_reader =
        node_.create_polling_subscription<ADCTrajectory>(kPlanningTopic, rclcpp::DefaultQoS());

    // pretty_health_reader = node_.create_polling_subscription<t2::common::PrettyHealth>(
    //     kPrettyHealthPlanningTopic, rclcpp::DefaultQoS()
    // [this](const std::shared_ptr<const t2::common::PrettyHealth>& ptr) {
    //   pretty_health_ = *ptr;
    // }
    // );

    truckmaker_data_out_reader = node_.create_polling_subscription<TruckMakerMessageOut>(
        kTruckMakerDataOutTopic, rclcpp::DefaultQoS()
        // [this](const std::shared_ptr<const TruckMakerMessageOut>& ptr) {
        //   truckmaker_data_out_ = truckmaker::FromProto(*ptr);
        // }
    );
  }  // if(use_sils)

  /* ===== Scenario =====*/
  if (!FromFile(planning_test_input, planning_test_input_filename)) {
    throw planning::PlanningModuleException("Invalid planning test input",
                                            planning::PlanningModuleErrorCode::INVALID_INPUT);
  }
  planning_test_input.map_dir =
      std::string(FULL_YATAGARASU_DIR_PREFIX) + "src/map/data/" + planning_test_input.map_dir;

  const std::string planning_test_input_str = ToJsonString(PLANNING_VNP(planning_test_input));
  T2_INFO << "planning_test_input=\n" << planning_test_input_str;

  // Make map available to all components
  auto& map_config = common::config::MapConfig::GetInstance();
  map_config.SetMapDir(planning_test_input.map_dir);

  /* ===== Planner Config (Read-only) =====*/
  {
    const std::string planner_config_file =
        common::runfiles::RuntimeDependencyManager::ResolvePath(kPlannerConfigFile);
    std::ifstream is(planner_config_file);
    T2_PLAN_CHECK_FULL(PlanningModuleErrorCode::INVALID_INPUT, is)
        << "Cannot read " << planner_config_file;
    cereal::JSONInputArchive archive(is);  // serialize to JSON
    archive(CEREAL_NVP(planner_config));
    T2_INFO << "Planner config is:\n" << ToJsonString(planner_config);
  }

  /* ===== TruckMaker Config (Read-only) =====*/
  {
    const std::string truckmaker_config_file =
        common::runfiles::RuntimeDependencyManager::ResolvePath(kTruckMakerConfigFile);
    std::ifstream is(truckmaker_config_file);
    T2_PLAN_CHECK_FULL(PlanningModuleErrorCode::INVALID_INPUT, is)
        << "Cannot read " << truckmaker_config_file;
    cereal::JSONInputArchive archive(is);  // serialize to JSON
    truckmaker::TruckMakerConfig truckmaker_config;
    archive(CEREAL_NVP(truckmaker_config));
    planning_test_input.use_truckmaker_ = truckmaker_config.use_truckmaker;
    T2_INFO << "TruckMakerConfig is:\n" << ToJsonString(truckmaker_config);
  }

  /* ===== Send PlanningTestMessage =====*/
  PlanningTestMessage planning_test_message;
  planning_test_message.filename = planning_test_input_filename;
  planning_test_message.scenario = planning_test_input_str;
  planning_test_message.command = "scenario";
  planning_test_message.header.creation_timestamp = node_.get_clock()->now().seconds() * 1e9;
  if (use_sils_) {
    planning_test_writer->publish(planning_test_message);
  }

  /* ===== Planning Config (Read-write) =====*/
  if (planning_test_input.opt_planning_config) {
    const auto& opt_planning_config = planning_test_input.opt_planning_config;
    const std::string planning_config_str = ToJsonString(*opt_planning_config, "planning_config");
    T2_INFO << "Update planning_config to the opt_planning_config:\n" << planning_config_str;
    UpdateParameter update_parameter;
    update_parameter.command = "update_planning_config";
    update_parameter.config_json_string = planning_config_str;
    if (use_sils_) {
      update_parameter_writer->publish(update_parameter);
    }
  }

  /* ===== Start from a lane id (optional) =====*/
  const std::string& start_lane_id = planning_test_input.start_lane_id;
  LocalizationInfo& localization_info = planning_test_input.localization_info;
  if (!start_lane_id.empty()) {
    const hdmap::LaneInfoConstPtr lane_ptr = GetLaneById(start_lane_id);
    auto [x, y] = GetXYFromLanePosition(lane_ptr, 0);
    T2_INFO << std::setprecision(12) << "Start from start_lane_id=" << lane_ptr->id().id()
            << ", x=" << x << ", y=" << y;
    localization_info.x = x;
    localization_info.y = y;
  }

  if (planning_test_input.use_input_heading) {
    AlignLocalizationWithHeading(localization_info, localization_info.heading);
  } else {
    AlignLocalizationWithHeading(localization_info);
  }

  // broadcast to TruckMakerComponent
  if (use_sils_) {
    localization_writer->publish(localization_info.ToProto());
  }

  /* ===== Stop position (optional) =====*/
  StopPosition& stop_position = planning_test_input.stop_position;
  const auto& stop_lane_id = planning_test_input.stop_lane_id;
  if (!stop_lane_id.empty()) {
    const hdmap::LaneInfoConstPtr lane_ptr = GetLaneById(stop_lane_id);
    auto [x, y] = GetXYFromLanePosition(lane_ptr, 0);
    T2_INFO << std::setprecision(12) << "Stop from stop_lane_id=" << lane_ptr->id().id()
            << ", x=" << x << ", y=" << y;
    stop_position.valid = true;
    stop_position.x = x;
    stop_position.y = y;
  }

  /* ===== Set up obstacle velocity profiles =====*/
  const std::map<int, Obstacle>& obstacle_map = planning_test_input.obstacle_map;
  const auto& obstacle_motion_profile_map = planning_test_input.obstacle_motion_profile_map;
  for (const auto& [obs_id, obs] : obstacle_map) {
    auto& trajectories = obs.trajectories;
    if (!trajectories.empty()) {
      auto& trajectory = trajectories[0];
      if (!trajectory.empty()) {
        const bool has_motion_profile = obstacle_motion_profile_map.count(obs_id);
        if (has_motion_profile) {
          const auto& motion_profile = obstacle_motion_profile_map.at(obs_id);
          obstacle_velocity_profile_map_.emplace(
              obs_id, GenerateVelocityProfile(motion_profile, trajectory[0].v, planner_config.dt));
        } else {
          // no Phases, so constant speed
          std::vector<double> velocity_profile = GenerateVelocityProfile(trajectory[0].v);
          obstacle_velocity_profile_map_.emplace(obs_id, velocity_profile);
        }  // if (has_motion_profile)
      }  // if (!trajectory.empty())
    }  // if (!trajectories.empty())
  }  // for (const auto& [obs_id, obs] : obstacle_map)

  T2_INFO << "obstacle_velocity_profile_map_=" << ToJsonString(obstacle_velocity_profile_map_);

  /* ===== Set up callback ====*/
  // to-do: Move to executable

  // using std::chrono_literals::operator""ms;
  // init_context.RegisterTimerEventCallback(
  //     "ProcPlanning",
  //     [&]() {
  //       /* ===== Teleport ===== */
  //       const size_t stepsize = planning_test_input.stepsize;

  //       bool run_cycle = !(i_run % stepsize);
  //       if (run_cycle) {
  //         if (i_run == 0) {
  //           ProcPlanning();
  //         } else if (i_run < planning_test_input.num_runs) {
  //           if (planning_test_input.use_truckmaker_) {
  //             run_cycle =
  //                 // either there is no stop_position, or
  //                 (!stop_position.valid)
  //                 // stop_position is NOT close to the ego
  //                 || (stop_position.valid &&
  //                     !IsCloseToStopPosition(truckmaker_data_out_, stop_position));
  //           } else {
  //             run_cycle =
  //                 // either there is no stop_position, or
  //                 (!stop_position.valid)
  //                 // stop_position is NOT close to the ego
  //                 ||
  //                 (stop_position.valid && !IsCloseToStopPosition(adc_trajectory_,
  //                 stop_position));
  //           }

  //           if (run_cycle) {
  //             // normal situation, where there is no stop_position, or
  //             // stop_position is not close
  //             if (planning_test_input.use_truckmaker_) {
  //               planning_test_input.chassis_info.speed_mps = truckmaker_data_out_.next_vhcl.v;
  //             } else {
  //               UpdatePlanningInputFromTrajectory(adc_trajectory_);
  //             }
  //             ProcPlanning();
  //           }
  //         }
  //       }
  //       ++i_run;
  //     },
  //     100ms);
}

void PlanningTestSimulationComponent::Proc() {
  /* ===== Teleport ===== */
  const size_t stepsize = planning_test_input.stepsize;
  StopPosition& stop_position = planning_test_input.stop_position;

  bool run_cycle = !(i_run % stepsize);
  if (run_cycle) {
    if (i_run == 0) {
      ProcPlanning();
    } else if (i_run < planning_test_input.num_runs) {
      if (planning_test_input.use_truckmaker_) {
        run_cycle =
            // either there is no stop_position, or
            (!stop_position.valid)
            // stop_position is NOT close to the ego
            || (stop_position.valid && !IsCloseToStopPosition(truckmaker_data_out_, stop_position));
      } else {
        run_cycle =
            // either there is no stop_position, or
            (!stop_position.valid)
            // stop_position is NOT close to the ego
            || (stop_position.valid && !IsCloseToStopPosition(adc_trajectory_, stop_position));
      }

      if (run_cycle) {
        // normal situation, where there is no stop_position, or
        // stop_position is not close
        if (planning_test_input.use_truckmaker_) {
          planning_test_input.chassis_info.speed_mps = truckmaker_data_out_.next_vhcl.v;
        } else {
          UpdatePlanningInputFromTrajectory(adc_trajectory_);
        }
        ProcPlanning();
      }
    }
  }
  ++i_run;
}

std::tuple<PredictionObstacles, Chassis, LocalizationEstimate>
PlanningTestSimulationComponent::GeneratePlanningComponentInput() {
  // mutable inputs
  std::map<int, Obstacle>& obstacle_map = planning_test_input.obstacle_map;
  ChassisInfo& chassis_info = planning_test_input.chassis_info;
  LocalizationInfo& localization_info = planning_test_input.localization_info;
  // non-mutable inputs
  const bool is_from_mcap = planning_test_input.is_from_mcap;
  const std::map<double, LaneChangeObstacle>& lane_change_obstacle_map =
      planning_test_input.lane_change_obstacle_map;
  const std::map<double, LaneChangeDriverTriggerInfo>& lane_change_driver_triggers =
      planning_test_input.lane_change_driver_triggers;
  const size_t stepsize = planning_test_input.use_truckmaker_
                              ? 1
                              : planning_test_input.stepsize;  ///< TruckMaker does not likely
                                                               ///< support "teleport" feature

  const double simulation_time = i_run * planner_config.dt;
  auto opt_lane_change_obs =
      GetLaneChangeObstacle(planner_config, lane_change_obstacle_map, simulation_time);

  localization_info.timestamp_sec = node_.get_clock()->now().seconds();  // update time

  if (!std::isfinite(init_timestamp)) {
    init_timestamp = localization_info.timestamp_sec;
    T2_INFO << std::setprecision(12) << "init_timestamp=" << init_timestamp;
  }

  for (auto& [obs_id, obs] : obstacle_map) {
    auto& trajectories = obs.trajectories;
    for (std::vector<Obstacle::TrajectoryXYPoint>& trajectory : trajectories) {
      const size_t num_points = trajectory.size();
      if (num_points == 0) {
        T2_WARN << "No trajectory points in trajectory for obs_id=" << obs_id;
      } else {
        // trajectory has at least one trajectory point
        const bool is_first_cycle = i_run == 0;
        if (is_first_cycle) {
          // the first cycle
          if (num_points >= planner_config.n_points) {
            // enough points; do nothing
          } else {
            if (!is_from_mcap) {
              // trajectory is not from mcap, so may not have enough points
              // need propagating
              auto& velocity_profile = obstacle_velocity_profile_map_.at(obs_id);
              if (opt_lane_change_obs && obs_id == opt_lane_change_obs->id) {
                trajectory = GenerateObstacleLaneChangeTrajectory(planner_config, trajectory[0],
                                                                  velocity_profile,
                                                                  opt_lane_change_obs->direction);
              } else {
                trajectory = GenerateObstacleLaneKeepTrajectory(planner_config, trajectory[0],
                                                                velocity_profile);
              }
            }
          }
        } else {
          // NOT the first cycle
          if (num_points > planner_config.n_points) {
            // more points than enough
            trajectory.erase(trajectory.begin());  ///< remove the first point
                                                   ///< to advance 0.1 sec
          } else {
            // safety check
            T2_PLAN_CHECK(stepsize < num_points)
                << "Not enough trajectory points: stepsize=" << stepsize << "<" << num_points
                << "=num_points";

            if (!is_from_mcap) {
              // fast foward obstacles' velocities
              auto& velocity_profile = obstacle_velocity_profile_map_.at(obs_id);
              if (stepsize < velocity_profile.size()) {
                velocity_profile.erase(velocity_profile.begin(),
                                       velocity_profile.begin() + stepsize);
              } else {
                velocity_profile = {velocity_profile.back()};  // handle the case where stepsize
                                                               // is too large
              }

              // trajectory is not from mcap, so may not have enough points
              // need propagating
              if (opt_lane_change_obs && obs_id == opt_lane_change_obs->id) {
                trajectory = GenerateObstacleLaneChangeTrajectory(
                    planner_config, trajectory[stepsize], velocity_profile,
                    opt_lane_change_obs->direction);
              } else {
                trajectory = GenerateObstacleLaneKeepTrajectory(
                    planner_config, trajectory[stepsize], velocity_profile);
              }
            }
          }
        }  // if (is_first_cycle)

        obs.theta = GetHeadingFromXY(trajectory[0].x, trajectory[0].y);
        obs.timestamp = node_.get_clock()->now().seconds();
      }  // if (num_points == 0)
    }  // for (auto& trajectory : trajectories)
  }  // for (auto& [obs_id, obs] : obstacle_map)

  // Ego's Lane Change
  LaneChangeDriverTriggerInfo lane_change_info =
      GetLaneChangeDriverTriggerInfo(lane_change_driver_triggers, simulation_time);
  if (use_sils_) {
    planning_request_writer->publish(
        lane_change_info.ToPlanningRequest(localization_info.x, localization_info.y));
  }

  if (!planning_test_input.use_truckmaker_) {
    if (use_sils_) {
      BroadcastTransform_(localization_info, transform_stamped_);
    }
  }

  return {ToProto(obstacle_map), chassis_info.ToProto(), localization_info.ToProto()};
}

void PlanningTestSimulationComponent::ProcPlanning() {
  auto [prediction_obstacles, chassis, localization_estimate] =
      this->GeneratePlanningComponentInput();
  // Update all proto messages
  /*===== Trigger PlanningComponent::Proc =====*/
  if (use_sils_) {
    if (!planning_test_input.use_truckmaker_) {
      localization_writer->publish(localization_estimate);
    }
    chassis_writer->publish(chassis);
    prediction_writer->publish(prediction_obstacles);  ///< Proc

    T2_INFO << "Run another cycle i_run=" << i_run;
    opt_timer_.emplace();
  }
}

void PlanningTestSimulationComponent::UpdatePlanningInputFromTrajectory(
    const ADCTrajectory& adc_trajectory) {
  const size_t stepsize = planning_test_input.stepsize;

  auto& chassis_info = planning_test_input.chassis_info;
  auto& localization_info = planning_test_input.localization_info;

  const auto& only_trajectory = adc_trajectory.only_trajectory;
  const size_t num_points = only_trajectory.size();

  if (num_points <= stepsize) {
    T2_WARN << "num_points=" << num_points << "<= stepsize=" << stepsize;
    return;
  }

  T2_PLAN_CHECK(num_points > stepsize)
      << "only_trajectory.size()=" << num_points << ", stepsize=" << stepsize;
  const auto& /* TrajectoryPoint */ next_traj_point = only_trajectory[stepsize];

  // update chassis
  chassis_info.speed_mps = next_traj_point.v;

  // update localization
  const PathPoint& path_point = next_traj_point.path_point;
  localization_info.x = path_point.x;
  localization_info.y = path_point.y;

  localization_info.vx = next_traj_point.v;
  localization_info.vy = localization_info.vz = 0.0;
  AlignLocalizationWithHeading(localization_info, path_point.theta);

  // vehicle reference frame; see planning_vehicle_state_creator.cc
  localization_info.ay = next_traj_point.a;

  T2_INFO << std::setprecision(12) << "UpdatePlanningInputFromTrajectory: x=" << localization_info.x
          << ", y=" << localization_info.y;
}

void PlanningTestSimulationComponent::BroadcastTransform_(const LocalizationInfo& localization_info,
                                                          TransformStamped& transform_stamped) {
  // Refer to DynamicsSimulatorComponent::Process
  transform_stamped.header.stamp.sec = node_.get_clock()->now().seconds();
  transform_stamped.header.stamp.nanosec = transform_stamped.header.stamp.sec * 1e9;
  auto& translation = transform_stamped.transform.translation;
  translation.x = localization_info.x;
  translation.y = localization_info.y;
  translation.z = localization_info.z;
  auto& rotation = transform_stamped.transform.rotation;
  rotation.w = localization_info.qw;
  rotation.x = localization_info.qx;
  rotation.y = localization_info.qy;
  rotation.z = localization_info.qz;
  tf2_broadcaster.sendTransform(transform_stamped);
}

bool PlanningTestSimulationComponent::IsCloseToStopPosition(
    const ADCTrajectory& adc_trajectory, const StopPosition& stop_position) const {
  const size_t stepsize = planning_test_input.stepsize;

  const auto& only_trajectory = adc_trajectory.only_trajectory;
  const size_t num_points = only_trajectory.size();
  T2_PLAN_CHECK(num_points > stepsize) << "num_points=" << num_points << ", stepsize=" << stepsize;
  const TrajectoryPoint& next_traj_point = only_trajectory[stepsize];

  // update localization
  const PathPoint& path_point = next_traj_point.path_point;

  const double curr_x = path_point.x;
  const double curr_y = path_point.y;
  const bool is_close = stop_position.is_close(curr_x, curr_y);

  T2_INFO << "IsCloseToStopPosition: " << std::boolalpha << std::setprecision(12)
          << "is_close=" << is_close << ", curr_x=" << curr_x << ", curr_y=" << curr_y
          << ", target_x=" << stop_position.x << ", target_y=" << stop_position.y
          << ", dist=" << stop_position.dist(curr_x, curr_y);

  if (is_close) {
    if (!std::isfinite(stop_timestamp)) {
      stop_timestamp = node_.get_clock()->now().seconds();
      T2_INFO << std::setprecision(12)
              << "IsCloseToStopPosition: At stop_timestamp=" << stop_timestamp
              << ", the ego is close to " << ToJsonString(stop_position) << "\n"
              << "Suggested num_runs=" << (stop_timestamp - init_timestamp) / planner_config.dt;

      PlanningTestMessage planning_test_message;
      planning_test_message.command = "shutdown";
      planning_test_message.header.creation_timestamp = stop_timestamp * 1e9;
      if (use_sils_) {
        planning_test_writer->publish(planning_test_message);
      }
    }
  }

  return is_close;
}

bool PlanningTestSimulationComponent::IsCloseToStopPosition(
    const TruckMakerDataOut& truckmaker_data_out, const StopPosition& stop_position) const {
  // update localization
  auto& localization_info = planning_test_input.localization_info;
  const auto& next_vhcl = truckmaker_data_out.next_vhcl;
  const double curr_x = localization_info.x + next_vhcl.dx;
  const double curr_y = localization_info.y + next_vhcl.dy;
  const bool is_close = stop_position.is_close(curr_x, curr_y);

  T2_INFO << "IsCloseToStopPosition: " << std::boolalpha << std::setprecision(12)
          << "is_close=" << is_close << ", curr_x=" << curr_x << ", curr_y=" << curr_y
          << ", target_x=" << stop_position.x << ", target_y=" << stop_position.y
          << ", dist=" << stop_position.dist(curr_x, curr_y);

  if (is_close) {
    if (!std::isfinite(stop_timestamp)) {
      stop_timestamp = node_.get_clock()->now().seconds();
      T2_INFO << std::setprecision(12)
              << "IsCloseToStopPosition: At stop_timestamp=" << stop_timestamp
              << ", the ego is close to " << ToJsonString(stop_position) << "\n"
              << "Suggested num_runs=" << (stop_timestamp - init_timestamp) / planner_config.dt;

      PlanningTestMessage planning_test_message;
      planning_test_message.command = "shutdown";
      planning_test_message.header.creation_timestamp = stop_timestamp * 1e9;
      if (use_sils_) {
        planning_test_writer->publish(planning_test_message);
      }
    }
  }

  return is_close;
}

bool PlanningTestSimulationComponent::execute_impl() {
  auto trajectory_msgs = trajectory_reader->take();
  for (const auto& msg : trajectory_msgs) {
    if (msg.info().valid()) {
      adc_trajectory_ = msg.data();
      if (opt_timer_) {
        PlanningTestMessage planning_test_message;
        planning_test_message.elapsed_time = opt_timer_->elapsed();
        planning_test_message.command = "profile";
        planning_test_message.header.creation_timestamp = node_.get_clock()->now().seconds() * 1e-9;
        if (use_sils_) {
          planning_test_writer->publish(planning_test_message);
        }
        opt_timer_.reset();
      }
    }
  }

  auto truckmaker_data_out_msgs = truckmaker_data_out_reader->take();
  for (const auto& msg : truckmaker_data_out_msgs) {
    if (msg.info().valid()) {
      truckmaker_data_out_ = truckmaker::FromMessage(msg.data());
    }
  }

  return true;
}

::apex::executor::subscription_list
PlanningTestSimulationComponent::get_triggering_subscriptions_impl() const {
  return {trajectory_reader};
}

::apex::executor::subscription_list
PlanningTestSimulationComponent::get_non_triggering_subscriptions_impl() const {
  return {truckmaker_data_out_reader};
}
::apex::executor::publisher_list PlanningTestSimulationComponent::get_publishers_impl() const {
  return {planning_request_writer, chassis_writer,          localization_writer,
          prediction_writer,       update_parameter_writer, planning_test_writer};
}

}  // namespace t2::planning::test
