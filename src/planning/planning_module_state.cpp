// Copyright (c) 2025 T2 Inc. All rights reserved.
#include "src/planning/planning_module_state.hpp"

#include "src/planning/planning_macros.hpp"  // REGISTER_*_PLANNING_MSG

namespace t2::planning {

void UpdatePlanningModuleState(
    ::planning_msgs::msg::PlanningModuleStatus& planning_module_status,
    const PlanningConfiguration& planning_config, const IntentionTaskData& intention,
    const std::list<ReferenceLine>& /* reference_lines */,
    const TrajectoryPoint& planning_start_point,
    const ReferenceLineAndRouteSegments& lc_source_lane,
    const std::optional<ReferenceLineAndRouteSegments>& opt_lc_destination_lane,
    const ReferenceLineMap& reference_line_map) {
  REGISTER_INTER_PLANNING_MSG(PlanningModuleState);

  const bool is_normal_intention = intention.name.find("normal") != std::string::npos;

  // Update planning module state
  auto& planning_module_state = planning_module_status.state;
  switch (planning_module_state) {
    case PlanningModuleState::LANE_CHANGE_COMPLETE: {
      T2_INFO << "LANE_CHANGE_COMPLETE -> LANE_FOLLOW";
      planning_module_state = PlanningModuleState::LANE_FOLLOW;
      break;
    }
    case PlanningModuleState::LANE_FOLLOW: {
      if (!is_normal_intention) {
        T2_INFO << std::boolalpha
                << "LANE_FOLLOW -> LANE_CHANGE: is_normal_intention=" << is_normal_intention;
        planning_module_state = PlanningModuleState::LANE_CHANGE;
      }
      break;
    }
    case PlanningModuleState::LANE_CHANGE: {
      if (opt_lc_destination_lane && !is_normal_intention) {
        const double lane_keep_lateral_threshold =
            planning_config.lane_change_config.lane_keep_lateral_threshold;

        T2_PLAN_CHECK_FULL(PlanningModuleErrorCode::UNEXPECTED_STATE,
                           reference_line_map.count(ReferenceLineType::LANE_CHANGE_TARGET))
            << "Should have lane change target reference line";

        const auto& target_lane_reference_line =
            reference_line_map.at(ReferenceLineType::LANE_CHANGE_TARGET).reference_line;
        const auto [target_sl, angle_diff] =
            GetSLAndHeadingToReferenceLine(planning_start_point, target_lane_reference_line);
        const double l_target = target_sl.l;

        const bool is_lane_change_completed = std::fabs(l_target) <= lane_keep_lateral_threshold;

        T2_INFO << "l_target=" << l_target
                << ", lane_keep_lateral_threshold=" << lane_keep_lateral_threshold;

        if (is_lane_change_completed) {
          T2_INFO << "LANE_CHANGE -> LANE_CHANGE_COMPLETE: " << ", l_target=" << l_target
                  << ", lane_keep_lateral_threshold=" << lane_keep_lateral_threshold;
          planning_module_state = PlanningModuleState::LANE_CHANGE_COMPLETE;
        }
      } else {
        T2_INFO << std::boolalpha
                << "LANE_CHANGE -> LANE_CHANGE_CANCEL: "
                   "opt_lc_destination_lane.has_value()="
                << opt_lc_destination_lane.has_value()
                << ", is_normal_intention=" << is_normal_intention;
        planning_module_state = PlanningModuleState::LANE_CHANGE_CANCEL;
      }
      break;
    }
    case PlanningModuleState::LANE_CHANGE_CANCEL: {
      const auto& ego_lane_reference_line = lc_source_lane.reference_line;
      const auto [ego_sl, angle_diff] =
          GetSLAndHeadingToReferenceLine(planning_start_point, ego_lane_reference_line);
      const double l_ego = ego_sl.l;

      const double lane_keep_lateral_threshold =
          planning_config.lane_change_config.lane_keep_lateral_threshold;
      const bool is_lane_change_cancel_completed = l_ego <= lane_keep_lateral_threshold;

      T2_INFO << "l_ego=" << l_ego
              << ", lane_keep_lateral_threshold=" << lane_keep_lateral_threshold;

      if (is_lane_change_cancel_completed) {
        T2_INFO << "LANE_CHANGE_CANCEL -> LANE_FOLLOW: " << "l_ego=" << l_ego
                << ", lane_keep_lateral_threshold=" << lane_keep_lateral_threshold;
        planning_module_state = PlanningModuleState::LANE_FOLLOW;
      }
      break;
    }
    default: {
      T2_ERROR << "Unknown planning module state: " << static_cast<int>(planning_module_state);
    }
  }
}

}  // namespace t2::planning
