// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "driver_trigger.hpp"

namespace t2::planning {

namespace lane_change_trigger {

using lane_change_decider::LaneChangeDecision;

static bool IsLaneChanging(
    const std::shared_ptr<IntentionTaskData>& last_trajectory_intention_ptr) {
  if (last_trajectory_intention_ptr != nullptr && last_trajectory_intention_ptr->is_lane_change &&
      last_trajectory_intention_ptr->lane_change_state != LaneChangeState::INVALID) {
    if (last_trajectory_intention_ptr->lane_change_state == LaneChangeState::LANE_CHANGING) {
      return true;
    }
  }
  return false;
}

TurnSignal DriverTrigger::ConvertPlanningCommandToTurnSignal(
    const TurnSignal turn_signal, const PlanningCommand planning_command) {
  TurnSignal _turn_signal = turn_signal;
  // The `planning_command` emulates the lane change trigger in the simulator.
  if (planning_command == PlanningCommand::TURN_LEFT_SIGNAL_ON) {
    _turn_signal = TurnSignal::TURN_LEFT;
  } else if (planning_command == PlanningCommand::TURN_RIGHT_SIGNAL_ON) {
    _turn_signal = TurnSignal::TURN_RIGHT;
  }
  return _turn_signal;
}

bool DriverTrigger::KeepThePreviousTurnSignalDirection(const TurnSignal& turn_signal) {
  return opt_manual_turn_signal_ && opt_manual_turn_signal_.value() == turn_signal;
}

void DriverTrigger::UpdateManualTurnSignalState(const TurnSignal& turn_signal,
                                                const PlanningModuleState& planning_state,
                                                const bool trigger_geofenced, rclcpp::Node& node) {
  // We lock the driver trigger if the lane change is complete or canceled or if
  // the lane change is geofenced.
  if (planning_state == PlanningModuleState::LANE_CHANGE_COMPLETE ||
      planning_state == PlanningModuleState::LANE_CHANGE_CANCEL || trigger_geofenced) {
    driver_trigger_lock_ = true;
    opt_manual_turn_signal_start_time_.reset();
  }

  // We unlock the trigger if the signal is in neutral position or
  // if the current turn signal is different from the previous turn signal.
  // If the trigger is geofenced, we do not unlock it.
  if ((turn_signal == TurnSignal::TURN_NONE || !KeepThePreviousTurnSignalDirection(turn_signal)) &&
      !trigger_geofenced) {
    driver_trigger_lock_ = false;
  }

  if (!driver_trigger_lock_ &&
      (turn_signal == TurnSignal::TURN_LEFT || turn_signal == TurnSignal::TURN_RIGHT)) {
    // If the trigger is unlocked and the turn signal is right or left or the
    // turn signal is engaged for the first time, we update the manual turn
    // signal state.
    if (!KeepThePreviousTurnSignalDirection(turn_signal)) {
      opt_manual_turn_signal_start_time_ = node.get_clock()->now().seconds();
    }
  }

  // We save the state of the turn signal.
  opt_manual_turn_signal_ = turn_signal;
  T2_INFO << LogTurnSignalMessage(turn_signal);
}

LaneChangeDecision DriverTrigger::DecideIfLaneChangeNeeded(
    const PlanningModuleState& planning_state, const double abort_lane_change_time,
    const TurnSignal& turn_signal,
    const std::shared_ptr<IntentionTaskData>& last_trajectory_intention_ptr,
    const bool trigger_geofenced, rclcpp::Node& node) {
  // LaneChangeDecision data
  LaneChangeDecision::Status status = LaneChangeDecision::Status::UNSUPPORTED_CASE;
  LaneChangeDirection direction = LaneChangeDirection::NONE;

  // Calculate since when the winker has been trigered and
  // Determine if the driver trigger is locked or not.
  UpdateManualTurnSignalState(turn_signal, planning_state, trigger_geofenced, node);

  // If the driver trigger is locked, we don't do a lane change.
  if (driver_trigger_lock_) {
    direction = LaneChangeDirection::NONE;
    status = LaneChangeDecision::Status::UNSUPPORTED_CASE;
  } else if (turn_signal == TurnSignal::TURN_LEFT || turn_signal == TurnSignal::TURN_RIGHT) {
    // Determine the direction of the Driver trigger.
    direction = (turn_signal == TurnSignal::TURN_LEFT) ? LaneChangeDirection::LEFT
                                                       : LaneChangeDirection::RIGHT;
    status = LaneChangeDecision::Status::OK;

    // Verify that the trigger time is not older than the
    // abort_lane_change_attempt_after_sec parameter.
    // if the trigger is too old, the trigger is
    // canceled only if the lane change is not yet started.
    if (opt_manual_turn_signal_start_time_ && !IsLaneChanging(last_trajectory_intention_ptr)) {
      const double elapsed_seconds =
          node.get_clock()->now().seconds() - opt_manual_turn_signal_start_time_.value();
      if (elapsed_seconds > abort_lane_change_time) {
        T2_WARN << "Canceling lane change attempt. "
                << "elapsed_seconds: " << elapsed_seconds
                << "abort_lane_change_attempt_after_sec: " << abort_lane_change_time;
        direction = LaneChangeDirection::NONE;
        status = LaneChangeDecision::Status::ABORT_LANE_CHANGE_ATTEMPT_TOO_LONG_SIGNAL;

        driver_trigger_lock_ = true;
        opt_manual_turn_signal_start_time_.reset();
      }
    }
  } else {
    direction = LaneChangeDirection::NONE;
    status = LaneChangeDecision::Status::UNSUPPORTED_CASE;
  }

  LaneChangeDecision decision;
  decision.lane_change_direction = direction;
  decision.status = status;
  return decision;
}

// LOG
std::string DriverTrigger::LogTurnSignalMessage(TurnSignal turn_signal) {
  std::ostringstream os;
  os << "turn_signal = " << static_cast<int>(turn_signal) << ", manual_turn_signal_direction = ";
  if (opt_manual_turn_signal_) {
    os << static_cast<int>(*opt_manual_turn_signal_);
  } else {
    os << "nullopt";
  }
  os << ", manual_turn_signal_start_time = "
     << (opt_manual_turn_signal_start_time_
             ? std::to_string(opt_manual_turn_signal_start_time_.value())
             : "nullopt");
  return os.str();
};

}  // namespace lane_change_trigger
}  // namespace t2::planning
