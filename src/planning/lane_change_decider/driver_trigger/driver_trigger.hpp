// Copyright (c) 2025 T2 Inc. All rights reserved.

#pragma once

#include <common_msgs/msg/vehicle_signal.hpp>            // VehicleSignal, TurnSignal
#include <planning_msgs/msg/planning_module_status.hpp>  // PlanningModuleState
#include <planning_msgs/msg/planning_request.hpp>        // PlanningRequest, PlanningCommand
#include <rclcpp/rclcpp.hpp>                             // rclcpp::Node, rclcpp::Clock::SharedPtr

#include "src/planning/common/intention_task_data.hpp"                // IntentionTaskData
#include "src/planning/config/planning_config.hpp"                    // PlanningConfiguration
#include "src/planning/lane_change_decider/lane_change_decision.hpp"  // LaneChangeDecision
#include "src/planning/planning_macros.hpp"                           // REGISTER_*_PLANNING_MSG

namespace t2::planning {

REGISTER_INTER_PLANNING_MSG(PlanningModuleState);
REGISTER_INTER_COMMON_MSG(TurnSignal);
REGISTER_INTER_PLANNING_MSG(PlanningCommand);

namespace lane_change_trigger {

class DriverTrigger {
 public:
  DriverTrigger() = default;
  ~DriverTrigger() = default;

  lane_change_decider::LaneChangeDecision DecideIfLaneChangeNeeded(
      const PlanningModuleState& planning_state, const double abort_lane_change_time,
      const TurnSignal& turn_signal,
      const std::shared_ptr<IntentionTaskData>& last_trajectory_intention_ptr,
      const bool trigger_geofenced, rclcpp::Node& node);

  void UpdateManualTurnSignalState(const TurnSignal& turn_signal,
                                   const PlanningModuleState& planning_state,
                                   const bool trigger_geofenced, rclcpp::Node& node);

  bool KeepThePreviousTurnSignalDirection(const TurnSignal& turn_signal);

  static TurnSignal ConvertPlanningCommandToTurnSignal(const TurnSignal turn_signal,
                                                       const PlanningCommand planning_command);

  // Log
  std::string LogTurnSignalMessage(TurnSignal turn_signal);

  std::optional<TurnSignal> opt_manual_turn_signal_;

  std::optional<double> opt_manual_turn_signal_start_time_;
  bool driver_trigger_lock_ = true;
  bool lane_change_triggered_by_map = false;  // Wether or not the lane change is triggered by the
                                              // map information.

  TurnSignal map_lane_change_direction =
      TurnSignal::TURN_NONE;  // If the lane change is triggered by the map
                              // information, this variable will contain the
                              // direction of lane change.
};

}  // namespace lane_change_trigger
}  // namespace t2::planning
