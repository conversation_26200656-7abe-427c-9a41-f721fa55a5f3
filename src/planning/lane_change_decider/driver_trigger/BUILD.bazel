package(default_visibility = ["//visibility:public"])

cc_library(
    name = "driver_trigger",
    srcs = [
        "driver_trigger.cpp",
    ],
    hdrs = ["driver_trigger.hpp"],
    copts = [
        "-DMODULE_NAME=\\\"planning\\\"",
    ],
    deps = [
        "//src/interfaces/common_msgs",
        "//src/interfaces/planning_msgs",  # inter-module planning messages
        "//src/planning:planning_macros",  # REGISTER_*_PLANNING_MSG
        "//src/planning/common:intention_task_data",
        "//src/planning/config:planning_config",
        "//src/planning/lane_change_decider:lane_change_decision",
    ],
)
