load("@rules_cc//cc:defs.bzl", "cc_library")

package(default_visibility = ["//visibility:public"])

cc_library(
    name = "lane_change_trigger",
    srcs = [
        "lane_change_trigger.cpp",
    ],
    hdrs = ["lane_change_trigger.hpp"],
    copts = [
        "-DMODULE_NAME=\\\"planning\\\"",
    ],
    deps = [
        "//src/map/proto:map_id_cc_proto",  # ::t2::map::hdmap::Id
    ],
)

cc_library(
    name = "lane_change_decision",
    srcs = [
        "lane_change_decision.cpp",
    ],
    hdrs = ["lane_change_decision.hpp"],
    copts = [
        "-DMODULE_NAME=\\\"planning\\\"",
    ],
    deps = [
        ":lane_change_trigger",
    ],
)

cc_library(
    name = "lane_change_decider",
    srcs = [
        "lane_change_decider.cpp",
    ],
    hdrs = ["lane_change_decider.hpp"],
    copts = [
        "-DMODULE_NAME=\\\"planning\\\"",
    ],
    deps = [
        ":lane_change_decision",
        ":lane_change_trigger",
        "//src/common/core",  # T2_*
        "//src/interfaces/planning_msgs",  # inter-module planning messages
        "//src/planning:planning_macros",  # REGISTER_*_PLANNING_MSG
        "//src/planning/common:intention_task_data",
        "//src/planning/config:planning_config",
        "//src/planning/reference_line:reference_line_provider",
    ],
)

cc_library(
    name = "lane_change",
    srcs = [
        "lane_change.cpp",
    ],
    hdrs = [
        "lane_change.hpp",
    ],
    copts = [
        "-DMODULE_NAME=\\\"planning\\\"",
    ],
    deps = [
        ":lane_changeability",
        ":log_hmi",
        ":log_lane_changeability",
        "//src/planning/common:intention_task_data",
        "//src/planning/common:obstacle",  # Obstacle
        "//src/planning/common/planning_vehicle_state",
        "//src/planning/config:planner_config",  # PlannerConfig
        "//src/planning/config:planning_config",  # PlanningConfiguration
        "//src/planning/lane_change_decider/driver_trigger",  # DriverTrigger
        "//src/planning/planner/speed_planner:speed_planner_requirements",
        "//src/planning/reference_line:reference_line_provider",
        "@apex//grace/ros/rclcpp/rclcpp",  # rclcpp::Node
    ],
)

cc_library(
    name = "lane_changeability",
    srcs = [
        "lane_changeability.cpp",
    ],
    hdrs = [
        "lane_changeability.hpp",
    ],
    copts = [
        "-DMODULE_NAME=\\\"planning\\\"",
    ],
    deps = [
        ":lane_change_decider",
        "//src/planning/common:obstacle",  # ObstacleInformation
        "//src/planning/config:planning_config",  # PlanningConfiguration
    ],
)

cc_library(
    name = "log_lane_changeability",
    srcs = [
        "logging/log_lane_changeability.cpp",
    ],
    hdrs = [
        "logging/log_lane_changeability.hpp",
    ],
    copts = [
        "-DMODULE_NAME=\\\"planning\\\"",
    ],
    deps = [
        ":lane_changeability",
        "//src/interfaces/planning_msgs",  # inter-module planning messages
        "//src/planning:planning_macros",  # REGISTER_*_PLANNING_MSG
    ],
)

cc_library(
    name = "log_hmi",
    srcs = [
        "logging/log_hmi.cpp",
    ],
    hdrs = [
        "logging/log_hmi.hpp",
    ],
    copts = [
        "-DMODULE_NAME=\\\"planning\\\"",
    ],
    deps = [
        ":lane_change_decider",
        "//src/interfaces/planning_msgs",  # inter-module planning messages
    ],
)
