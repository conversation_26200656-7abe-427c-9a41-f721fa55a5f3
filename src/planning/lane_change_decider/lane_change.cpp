// Copyright (c) 2025 T2 Inc. All rights reserved.

/**
 * @file lane_change.cc
 *
 * @brief
 */
#include "src/planning/lane_change_decider/lane_change.hpp"

#include <planning_msgs/msg/obstacles.hpp>  // ::planning_msgs::msg::Obstacles

#include "src/planning/lane_change_decider/lane_change_trigger.hpp"           // LaneChangeDirection
#include "src/planning/planner/speed_planner/speed_planner_requirements.hpp"  // LaneChangeDirection

namespace t2::planning {

void AddCurrentObstaclesInformationToProto(
    const std::map<int, ObstacleInformation>& current_obstacles_information,
    ::planning_msgs::msg::Obstacles& obstacles_output);

/**
 * @brief   Function to calculate the progress of lane change maneuver.
 *
 * This function returns the progress of a lane change maneuver as a fractional
 * number between 0 and 1. The progress estimates how far the ego
 * vehicle has proceeded in the changing lanes process.
 * A return value of 0 signifies that the ego vehicle is still at the beginning
 * of the process (in the initial lane).
 * A return value of 1 signifies that the vehicle has fully changed to the
 * target lane.
 * If it's not possible to calculate the progress at the moment,
 * the function returns a 'nullopt'.
 *
 * @return std::optional<double> The progress of the lane change, a fractional
 * number between 0 and 1. If this value can't be calculated, return a
 * std::nullopt.
 */
std::optional<double> CalculateLaneChangeProgress(const PlanningVehicleState& vehicle_state,
                                                  const ReferenceLine& lc_source_ref_line,
                                                  const ReferenceLine& lc_destination_ref_line) {
  const common::math::Vec2d xy_point(vehicle_state.x, vehicle_state.y);
  const std::optional<SLPoint> opt_sl_point_from_ego_lane = lc_source_ref_line.XYToSL(xy_point);
  const std::optional<SLPoint> opt_sl_point_from_target_lane =
      lc_destination_ref_line.XYToSL(xy_point);
  if (!opt_sl_point_from_ego_lane) {
    T2_ERROR << "Failed to calculate SL point in relation to the center of the "
                "ego lane.";
    return std::nullopt;
  }
  if (!opt_sl_point_from_target_lane) {
    T2_ERROR << "Failed to calculate SL point in relation to the center of the "
                "target lane.";
    return std::nullopt;
  }
  const auto& sl_point_from_ego_lane = opt_sl_point_from_ego_lane.value();
  const auto& sl_point_from_target_lane = opt_sl_point_from_target_lane.value();
  const double l_ego = sl_point_from_ego_lane.l;
  const double l_target = sl_point_from_target_lane.l;
  // LC left to right: neg/(neg-pos); right to left: pos/(pos-neg)
  const double progress = l_ego / (l_ego - l_target);
  return std::min(1.0, std::max(0.0, progress));
}

std::tuple<bool, std::optional<double>, std::map<int, ObstacleInformation>> LaneChange(
    const PlanningConfiguration& config, const PlannerConfig& planner_config,
    std::optional<ReferenceLineAndRouteSegments>& opt_lc_destination_lane,
    const std::list<route_lane_manager::RouteSegments>& list_route_segments,
    const std::list<ReferenceLine>& list_reference_lines,
    PlanningModuleStatus& planning_module_status,
    const ReferenceLineAndRouteSegments& current_driving_lane,
    ReferenceLineAndRouteSegments& lc_source_lane, ReferenceLineMap& reference_line_map,
    const PlanningVehicleState& aligned_vehicle_state, const Advice& engage_advice,
    const TurnSignal& turn_signal, const PlanningCommand& planning_command,
    lane_change_decider::LaneChangeDecision& lane_change_decision,
    lane_change_trigger::DriverTrigger& lane_change_driver_trigger,
    std::shared_ptr<IntentionTaskData>& last_trajectory_intention_ptr,
    lane_change_decider::LaneChangeDecider& lane_change_decider,
    const TrajectoryPoint& planning_start_point, ADCTrajectory& output_trajectory,
    const PlanningVehicleState& projected_vehicle_state,
    const std::map<int, Obstacle>& obstacle_map, rclcpp::Node& node,
    //, HMINotification& hmi_notification,
    hdmap::LaneInfoConstPtr ego_lane) {
  SetLaneChangeSourceLaneBaseOnState(planning_module_status.state, current_driving_lane,
                                     /* output */ lc_source_lane);
  reference_line_map[ReferenceLineType::LANE_CHANGE_SOURCE] = lc_source_lane;

  // Compute LC progress
  std::optional<double> opt_lane_change_progress;
  if (opt_lc_destination_lane) {
    opt_lane_change_progress =
        CalculateLaneChangeProgress(aligned_vehicle_state, lc_source_lane.reference_line,
                                    opt_lc_destination_lane->reference_line);
  }

  // Determine if the lane change can/should be triggered based on the map
  // information. The rule is following for a left lane change: 0=<
  // lane_change_info. lc_dis < 1000 The rule is the following for right lane
  // change: -1500 <= lane_change_info. Lc_dis < -500
  double left_lane_change_upper_limit =
      config.lane_change_config.lane_change_distance_parameters.lane_change_default.left.upper;
  double left_lane_change_lower_limit =
      config.lane_change_config.lane_change_distance_parameters.lane_change_default.left.lower;
  double right_lane_change_upper_limit =
      config.lane_change_config.lane_change_distance_parameters.lane_change_default.right.upper;
  double right_lane_change_lower_limit =
      config.lane_change_config.lane_change_distance_parameters.lane_change_default.right.lower;
  if (ego_lane->lane().lc_info().type() == hdmap::LaneChangeInfo_LaneChangeType_LC_JARI) {
    left_lane_change_upper_limit =
        config.lane_change_config.lane_change_distance_parameters.lane_change_jari.left.upper;
    left_lane_change_lower_limit =
        config.lane_change_config.lane_change_distance_parameters.lane_change_jari.left.lower;
    right_lane_change_upper_limit =
        config.lane_change_config.lane_change_distance_parameters.lane_change_jari.right.upper;
    right_lane_change_lower_limit =
        config.lane_change_config.lane_change_distance_parameters.lane_change_jari.right.lower;
  } else if (ego_lane->lane().lc_info().type() ==
             hdmap::LaneChangeInfo_LaneChangeType_LC_TAKATSUKI) {
    left_lane_change_upper_limit =
        config.lane_change_config.lane_change_distance_parameters.lane_change_takatsuki.left.upper;
    left_lane_change_lower_limit =
        config.lane_change_config.lane_change_distance_parameters.lane_change_takatsuki.left.lower;
    right_lane_change_upper_limit =
        config.lane_change_config.lane_change_distance_parameters.lane_change_takatsuki.right.upper;
    right_lane_change_lower_limit =
        config.lane_change_config.lane_change_distance_parameters.lane_change_takatsuki.right.lower;
  } else if (ego_lane->lane().lc_info().type() == hdmap::LaneChangeInfo_LaneChangeType_LC_INASA) {
    left_lane_change_upper_limit =
        config.lane_change_config.lane_change_distance_parameters.lane_change_inasa.left.upper;
    left_lane_change_lower_limit =
        config.lane_change_config.lane_change_distance_parameters.lane_change_inasa.left.lower;
    right_lane_change_upper_limit =
        config.lane_change_config.lane_change_distance_parameters.lane_change_inasa.right.upper;
    right_lane_change_lower_limit =
        config.lane_change_config.lane_change_distance_parameters.lane_change_inasa.right.lower;
  }

  // We verify if the vehicle is approaching a lane change.
  // This information is use for the HMI.
  bool is_approaching_left_lane_change =
      (ego_lane->lane().lc_info().direction() ==
           hdmap::LaneChangeInfo_LaneChangeDirection_LC_LEFT &&
       ego_lane->lane().lc_info().lc_dis() < left_lane_change_lower_limit);

  bool is_approaching_right_lane_change =
      (ego_lane->lane().lc_info().direction() ==
           hdmap::LaneChangeInfo_LaneChangeDirection_LC_RIGHT &&
       ego_lane->lane().lc_info().lc_dis() < right_lane_change_lower_limit);

  // hmi approach merge right
  bool is_approaching_merge_right = (ego_lane->lane().lc_info().direction() ==
                                         hdmap::LaneChangeInfo_LaneChangeDirection_MG_RIGHT &&
                                     ego_lane->lane().lc_info().lc_dis() < 0.0);

  if (is_approaching_merge_right) {
    // to-do
    // t2::ad_monitor::ADEvent* event = hmi_notification.add_events();
    // event->set_type(t2::ad_monitor::AD_EVENT_TYPE_APPROACHING_LANE_CHANGE);
    // event->set_direction(t2::ad_monitor::DIRECTION_LEFT_TO_RIGHT);
  }

  // hmi approaching lane change
  // If the vehicle is approaching a lane change, we send an hmi event to inform
  // the driver.
  if (is_approaching_left_lane_change) {
    // t2::ad_monitor::ADEvent* event = hmi_notification.add_events();
    // event->set_type(t2::ad_monitor::AD_EVENT_TYPE_APPROACHING_LANE_CHANGE);
    // event->set_direction(t2::ad_monitor::DIRECTION_RIGHT_TO_LEFT);
  } else if (is_approaching_right_lane_change) {
    // t2::ad_monitor::ADEvent* event = hmi_notification.add_events();
    // event->set_type(t2::ad_monitor::AD_EVENT_TYPE_APPROACHING_LANE_CHANGE);
    // event->set_direction(t2::ad_monitor::DIRECTION_LEFT_TO_RIGHT);
  }

  bool left_lane_change_can_be_triggered =
      (ego_lane->lane().lc_info().direction() ==
           hdmap::LaneChangeInfo_LaneChangeDirection_LC_LEFT &&
       ego_lane->lane().lc_info().lc_dis() >= left_lane_change_lower_limit &&
       ego_lane->lane().lc_info().lc_dis() < left_lane_change_upper_limit);
  bool right_lane_change_can_be_triggered =
      (ego_lane->lane().lc_info().direction() ==
           hdmap::LaneChangeInfo_LaneChangeDirection_LC_RIGHT &&
       ego_lane->lane().lc_info().lc_dis() >= right_lane_change_lower_limit &&
       ego_lane->lane().lc_info().lc_dis() < right_lane_change_upper_limit);

  // hmi lane change allowed area
  // If the vehicle is located in an area that allows lane change, we send an
  // hmi event to inform the driver.
  if (left_lane_change_can_be_triggered) {
    // t2::ad_monitor::ADEvent* event = hmi_notification.add_events();
    // event->set_type(t2::ad_monitor::AD_EVENT_TYPE_LANE_CHANGE_ALLOWED_AREA);
    // event->set_direction(t2::ad_monitor::DIRECTION_RIGHT_TO_LEFT);
  } else if (right_lane_change_can_be_triggered) {
    // t2::ad_monitor::ADEvent* event = hmi_notification.add_events();
    // event->set_type(t2::ad_monitor::AD_EVENT_TYPE_LANE_CHANGE_ALLOWED_AREA);
    // event->set_direction(t2::ad_monitor::DIRECTION_LEFT_TO_RIGHT);
  }

  bool left_takeover_requested =
      (ego_lane->lane().lc_info().direction() ==
           hdmap::LaneChangeInfo_LaneChangeDirection_LC_LEFT &&
       ego_lane->lane().lc_info().lc_dis() >= left_lane_change_upper_limit);
  bool right_takeover_requested =
      (ego_lane->lane().lc_info().direction() ==
           hdmap::LaneChangeInfo_LaneChangeDirection_LC_RIGHT &&
       ego_lane->lane().lc_info().lc_dis() >= right_lane_change_upper_limit);
  bool takeover_requested = (left_takeover_requested || right_takeover_requested);

  if (takeover_requested) {
    // to-do
    // t2::ad_monitor::ADEvent* event = hmi_notification.add_events();
    // event->set_type(t2::ad_monitor::AD_EVENT_TYPE_TAKEOVER_REQUEST);
  }

  // Verification of geo fencing
  // Verify if the ego vehicle is currently on a segment that allow to perform
  // a lane change. We verify the geo fencing for both direction as a segment
  // allow only a one direction lane change.
  // if the geofenced variable is true, the lane change cannot be performed.
  bool left_trigger_geofenced = false;
  bool right_trigger_geofenced = false;
  // After the start of the lane change we stop to verify the geofencing
  // because the current segment is changing during the lane change.
  // Because the geofencing is checked only before to start the lane change,
  // the lane change can still be canceled by setting the signal to
  // the opposite direction of the lane change to cancel it.

  // if the the short winker specification is used we veriy if the blinker is
  // turned off before to activate the geo fencing. on the other hand if the
  // long winker specification is used, we don't verify the blinker state,
  // verifying the planning module status is sufficient.
  bool use_geo_fencing = config.trigger_config.enable_geo_fencing &&
                         !(planning_module_status.state == PlanningModuleState::LANE_CHANGE);
  if (!config.use_long_winker) {
    use_geo_fencing &= !lane_change_decider.opt_blinker_start_time;
  }

  if (use_geo_fencing) {
    left_trigger_geofenced = !left_lane_change_can_be_triggered;
    right_trigger_geofenced = !right_lane_change_can_be_triggered;
    // If we have already blinked, then we are not geofenced!
  }

  if (engage_advice == Advice::KEEP_ENGAGED || config.lane_changed_allowed_when_ready_to_engage) {
    // Convertion of the planning command to turn signal.
    // The planning command is used in simualation to simulate a changing turn
    // signal.

    TurnSignal _turn_signal =
        lane_change_trigger::DriverTrigger::ConvertPlanningCommandToTurnSignal(turn_signal,
                                                                               planning_command);

    // DRIVER TRIGGER FILTERING
    // Block the turn signal if the driver trigger is disabled.

    if (!config.trigger_config.enable_driver_trigger) {
      _turn_signal = TurnSignal::TURN_NONE;
    }

    // AUTOMATIC LANE CHANGE TRIGGERED BY MAP
    // We transfer the turn signal information from the map
    // to simplify the implementation, we virtually control the turn signal when
    // the lane change is triggered throught the map.
    if (config.trigger_config.enable_automatic_lane_change) {
      // If the lane change has been previously triggered by the map we continue
      // using the previous signal.
      if (lane_change_driver_trigger.lane_change_triggered_by_map) {
        _turn_signal = lane_change_driver_trigger.map_lane_change_direction;
      }

      // We verify if the lane change is triggered by the map
      if (!lane_change_driver_trigger.lane_change_triggered_by_map) {
        // If the lane change is triggered by the map, we first send a TURN_NONE
        // signal to initialize the system.
        if (left_lane_change_can_be_triggered || right_lane_change_can_be_triggered) {
          _turn_signal = TurnSignal::TURN_NONE;
          lane_change_driver_trigger.lane_change_triggered_by_map = true;
        }

        if (left_lane_change_can_be_triggered) {
          lane_change_driver_trigger.map_lane_change_direction = TurnSignal::TURN_LEFT;
        } else if (right_lane_change_can_be_triggered) {
          lane_change_driver_trigger.map_lane_change_direction = TurnSignal::TURN_RIGHT;
        }
      }
    }

    // Block the turn signal depending on the geofencing.
    // If the driver is triggering the lane change, the lane change is blocked
    // if the location or the direction is wrong. To block the lane change we
    // keep the turn signal variable value to TURN_NONE.
    bool trigger_geofenced = false;
    if ((_turn_signal == TurnSignal::TURN_LEFT && left_trigger_geofenced) ||
        (_turn_signal == TurnSignal::TURN_RIGHT && right_trigger_geofenced)) {
      trigger_geofenced = true;
    }

    // Determine a lane change decision based on the determined turn signal
    // value.
    lane_change_decision = lane_change_driver_trigger.DecideIfLaneChangeNeeded(
        planning_module_status.state, config.trigger_config.abort_lane_change_attempt_after_sec,
        _turn_signal, last_trajectory_intention_ptr, trigger_geofenced, node);

    // If the lane change is canceled by the timer, or if the lane change is
    // canceled or completed, we reset the map lane change trigger.
    if (planning_module_status.state == PlanningModuleState::LANE_CHANGE_COMPLETE ||
        planning_module_status.state == PlanningModuleState::LANE_CHANGE_CANCEL ||
        lane_change_decision.status == lane_change_decider::LaneChangeDecision::Status::
                                           ABORT_LANE_CHANGE_ATTEMPT_TOO_LONG_SIGNAL ||
        trigger_geofenced) {
      lane_change_driver_trigger.lane_change_triggered_by_map = false;
      lane_change_driver_trigger.map_lane_change_direction = TurnSignal::TURN_NONE;
    }

    // Update the destination lane.
    opt_lc_destination_lane = lane_change_decider.UpdateDestinationLane(
        planning_module_status.state, config.lane_change_config.cancelation_threshold,
        opt_lane_change_progress, lane_change_decision, reference_line_map,
        opt_lc_destination_lane);

    // Update the reference lines of the source lane and destination lane
    // obtained from the previous planning cycle to the latest state
    UpdateLaneChangeSourceAndDestinationReferenceLine(
        /* input */
        planning_module_status.state, list_reference_lines, list_route_segments,
        /* output*/
        lc_source_lane, opt_lc_destination_lane);

    reference_line_map[ReferenceLineType::LANE_CHANGE_SOURCE] = lc_source_lane;
    if (opt_lc_destination_lane &&
        planning_module_status.state != PlanningModuleState::LANE_CHANGE_COMPLETE) {
      reference_line_map[ReferenceLineType::LANE_CHANGE_TARGET] = *opt_lc_destination_lane;
    }

    // Set cancel_position for a cancellation path.
    if (planning_module_status.state == PlanningModuleState::LANE_CHANGE) {
      // We need to set cancel_position every time during LANE_CHANGE because we
      // cannot estimate whether we should use cancel_position at the initial
      // frame of cancellation.
      const auto& ego_lane_reference_line = lc_source_lane.reference_line;
      const common::math::Vec2d ego_position(aligned_vehicle_state.x, aligned_vehicle_state.y);
      const double angle_diff = std::get<1>(
          GetSLAndHeadingToReferenceLine(planning_start_point, ego_lane_reference_line));

      // set PlanningModuleStatus for LANE_CHANGE_CANCEL
      planning_module_status.cancel_position.x = aligned_vehicle_state.x;
      planning_module_status.cancel_position.y = aligned_vehicle_state.y;
      planning_module_status.cancel_heading_diff = angle_diff;
    }
  }

  // Based on the trigger, we send a command to turn on the corresponding winker
  // light.
  if (lane_change_driver_trigger.opt_manual_turn_signal_start_time_) {
    lane_change_decider.opt_trigger_start_time_ =
        lane_change_driver_trigger.opt_manual_turn_signal_start_time_;
  } else {
    // lane change trigger reset
    lane_change_driver_trigger.opt_manual_turn_signal_start_time_.reset();
    lane_change_driver_trigger.driver_trigger_lock_ = true;
    lane_change_driver_trigger.lane_change_triggered_by_map = false;
    lane_change_driver_trigger.map_lane_change_direction = TurnSignal::TURN_NONE;

    // lane change decider reset
    lane_change_decider.opt_trigger_last_decision_.reset();
    lane_change_decider.opt_trigger_start_time_.reset();
    lane_change_decider.opt_blinker_start_time.reset();
  }

  const auto trigger_decision = lane_change_decider.opt_trigger_last_decision_;

  // Reference Line processing for lane change.
  auto ego_pos_x = projected_vehicle_state.x;
  auto ego_pos_y = projected_vehicle_state.y;
  std::optional<ReferenceLineAndRouteSegments> ego_right_reference_line;
  std::optional<ReferenceLineAndRouteSegments> ego_left_reference_line;
  if (reference_line_map.count(ReferenceLineType::RIGHT)) {
    ego_right_reference_line = reference_line_map.at(ReferenceLineType::RIGHT);
  }
  if (reference_line_map.count(ReferenceLineType::LEFT)) {
    ego_left_reference_line = reference_line_map.at(ReferenceLineType::LEFT);
  }

  // When the lane change has started, the reference line used is the
  // LANE_CHANGE_TARGET so we avoid loosing the reference line when the ego
  // enters the target lane.
  if (trigger_decision && reference_line_map.count(ReferenceLineType::LANE_CHANGE_TARGET)) {
    const auto& target_reference_line =
        reference_line_map.at(ReferenceLineType::LANE_CHANGE_TARGET);
    switch (trigger_decision->lane_change_direction) {
      case lane_change_trigger::LaneChangeDirection::RIGHT: {
        ego_right_reference_line = target_reference_line;
        break;
      }
      case lane_change_trigger::LaneChangeDirection::LEFT: {
        ego_left_reference_line = target_reference_line;
        break;
      }
      default: {
      }
    }
  }

  // Compute ego SL position.
  common::math::Vec2d ego_xy(ego_pos_x, ego_pos_y);
  const std::optional<SLPoint> opt_sl_point =
      reference_line_map.at(ReferenceLineType::CURRENT).reference_line.XYToSL(ego_xy);
  T2_PLAN_CHECK(opt_sl_point) << "Failed to compute SL: x=" << ego_pos_x << ", y=" << ego_pos_y;
  const double ego_s = opt_sl_point->s;
  const double ego_l = opt_sl_point->l;

  // Compute the Ego Lane width.
  double ego_lane_right_width = 0.0;
  double ego_lane_left_width = 0.0;
  reference_line_map.at(ReferenceLineType::CURRENT)
      .reference_line.GetLaneWidth(ego_s, ego_lane_left_width, ego_lane_right_width);

  // OBSTACLES PROCESSING
  // We compute the current obstacles information that will be used to determine
  // the lane changeability.
  std::map<int, ObstacleInformation> obstacles_information =
      ComputeObstaclesInformation(obstacle_map, reference_line_map.at(ReferenceLineType::CURRENT),
                                  ego_right_reference_line, ego_left_reference_line, ego_s, ego_l,
                                  ego_lane_right_width, ego_lane_left_width, planner_config);

  // We pass the distances information to the proto.
  AddCurrentObstaclesInformationToProto(obstacles_information, output_trajectory.debug.obstacles);

  // LANE CHANGEABILITY
  auto lane_changeability_distances =
      LaneChangeability::DetermineLaneChangeabilityInformation(obstacles_information);

  const auto& [lane_changeability_info_right, lane_changeability_info_left,
               lane_changeability_info_ego] = lane_changeability_distances;

  const auto& lane_changeable_parameters = config.lane_change_config.lane_changeable_parameters;

  // Lane changeability ego
  // ego front vehicle distance
  std::optional<double> safe_ego_front;
  if (lane_changeability_info_ego->d_front) {
    // ACC requirement parameters
    const auto& acc_requirement_parameters =
        planner_config.trajectory_planner_config.acc_requirement_parameters;
    const double d0_target = acc_requirement_parameters.d0_target;
    const double a_ego_comf = acc_requirement_parameters.a_ego_comf;
    const double a_front_max = acc_requirement_parameters.a_front_max;
    double target_distance = ComputeTargetDistance(projected_vehicle_state.linear_velocity,
                                                   lane_changeability_info_ego->v_front.value(),
                                                   d0_target, a_ego_comf, a_front_max);
    safe_ego_front =
        lane_changeable_parameters.d_safe_ego_parameters.d_target_ratio * target_distance;
  }

  // Lane changeability right
  // we compute s_critical for the obstacle in the right lane
  auto s_critical_right = LaneChangeability::GetSCriticalToRearObstacle(
      projected_vehicle_state.linear_velocity, lane_changeability_info_right->v_rear,
      lane_changeable_parameters);

  // Safe rear vehicle right
  // We compute the safe rear vehicle distance for the right lane
  const std::optional<double> safe_rear_right = LaneChangeability::GetDSafeRear(
      projected_vehicle_state.linear_velocity, lane_changeability_info_right->v_rear,
      lane_changeable_parameters.d_safe_rear_parameters);

  // Safe front vehicle right
  // We compute the safe front vehicle distance for the right lane
  const std::optional<double> safe_front_right = LaneChangeability::GetDSafeFront(
      projected_vehicle_state.linear_velocity, lane_changeability_info_right->v_front,
      lane_changeable_parameters.d_safe_front_parameters);

  bool is_lane_changeable_right_lane = LaneChangeability::IsLaneChangeable(
      lane_changeability_info_right->d_front, lane_changeability_info_right->d_rear,
      lane_changeability_info_ego->d_front, s_critical_right, safe_rear_right, safe_front_right,
      safe_ego_front, lane_changeable_parameters.min_d_front, lane_changeable_parameters.min_d_rear,
      config.lane_change_config.use_s_critical_for_lane_change,
      config.lane_change_config.use_d_safe_rear_for_lane_change,
      config.lane_change_config.use_d_safe_front_for_lane_change,
      config.lane_change_config.use_d_safe_ego_for_lane_change);

  // Lane changeability left
  // we compute s_critical for the obstacle in the left lane
  auto s_critical_left = LaneChangeability::GetSCriticalToRearObstacle(
      projected_vehicle_state.linear_velocity, lane_changeability_info_left->v_rear,
      lane_changeable_parameters);

  // Safe rear vehicle left
  // We compute the safe rear vehicle distance for the left lane
  const std::optional<double> safe_rear_left = LaneChangeability::GetDSafeRear(
      projected_vehicle_state.linear_velocity, lane_changeability_info_left->v_rear,
      lane_changeable_parameters.d_safe_rear_parameters);

  // Safe front vehicle left
  // We compute the safe front vehicle distance for the left lane
  const std::optional<double> safe_front_left = LaneChangeability::GetDSafeFront(
      projected_vehicle_state.linear_velocity, lane_changeability_info_left->v_front,
      lane_changeable_parameters.d_safe_front_parameters);

  bool is_lane_changeable_left_lane = LaneChangeability::IsLaneChangeable(
      lane_changeability_info_left->d_front, lane_changeability_info_left->d_rear,
      lane_changeability_info_ego->d_front, s_critical_left, safe_rear_left, safe_front_left,
      safe_ego_front, lane_changeable_parameters.min_d_front, lane_changeable_parameters.min_d_rear,
      config.lane_change_config.use_s_critical_for_lane_change,
      config.lane_change_config.use_d_safe_rear_for_lane_change,
      config.lane_change_config.use_d_safe_front_for_lane_change,
      config.lane_change_config.use_d_safe_ego_for_lane_change);

  auto& vehicle_signal = output_trajectory.decision.vehicle_signal;
  if (trigger_decision && lane_change_decider.opt_trigger_start_time_) {
    if (!config.use_long_winker) {
      if (trigger_decision->lane_change_direction ==
          lane_change_trigger::LaneChangeDirection::RIGHT) {
        if (is_lane_changeable_right_lane) {
          if (!lane_change_decider.opt_blinker_start_time) {
            T2_INFO << "Set blinker start time; ready to do lane change to right";
            lane_change_decider.opt_blinker_start_time = node.get_clock()->now().seconds();
          } else {
            T2_INFO << "Keep blinking right";
            vehicle_signal.turn_signal = TurnSignal::TURN_RIGHT;
          }
        } else {
          if (lane_change_decider.opt_blinker_start_time) {
            T2_INFO << std::boolalpha
                    << "We have blinked already, so we continue the lane change "
                       "to right, even if is_lane_changeable_right_lane="
                    << is_lane_changeable_right_lane;
            vehicle_signal.turn_signal = TurnSignal::TURN_RIGHT;
            is_lane_changeable_right_lane = true;
          }
        }
      } else if (trigger_decision->lane_change_direction ==
                 lane_change_trigger::LaneChangeDirection::LEFT) {
        if (is_lane_changeable_left_lane) {
          if (!lane_change_decider.opt_blinker_start_time) {
            T2_INFO << "Set blinker start time; ready to do lane change to left";
            lane_change_decider.opt_blinker_start_time = node.get_clock()->now().seconds();
          } else {
            T2_INFO << "Keep blinking left";
            vehicle_signal.turn_signal = TurnSignal::TURN_LEFT;
          }
        } else {
          if (lane_change_decider.opt_blinker_start_time) {
            T2_INFO << std::boolalpha
                    << "We have blinked already, so we continue the lane change "
                       "to left, even if is_lane_changeable_left_lane="
                    << is_lane_changeable_left_lane;
            vehicle_signal.turn_signal = TurnSignal::TURN_LEFT;
            is_lane_changeable_left_lane = true;
          }
        }
      }
    } else {
      if (trigger_decision->lane_change_direction ==
          lane_change_trigger::LaneChangeDirection::RIGHT) {
        // we activate the turn signal from the moment the truck enters the lane
        // change allowed area.
        vehicle_signal.turn_signal = TurnSignal::TURN_RIGHT;
        if (!lane_change_decider.opt_blinker_start_time) {
          T2_INFO << "Set blinker start time; ready to do lane change to right";
          lane_change_decider.opt_blinker_start_time = node.get_clock()->now().seconds();
        }

        // after the start of the lane change, it cannot be canceled.
        // to prevent the cancel, we make the right lane changeability always
        // true.
        if (planning_module_status.state == PlanningModuleState::LANE_CHANGE) {
          T2_INFO << "Have started lane change to right before; must finish lane change to end";
          is_lane_changeable_right_lane = true;
        }
      } else if (trigger_decision->lane_change_direction ==
                 lane_change_trigger::LaneChangeDirection::LEFT) {
        // we activate the turn signal from the moment the truck enters the lane
        // change allowed area.
        vehicle_signal.turn_signal = TurnSignal::TURN_LEFT;
        if (!lane_change_decider.opt_blinker_start_time) {
          T2_INFO << "Set blinker start time; ready to do lane change to left";
          lane_change_decider.opt_blinker_start_time = node.get_clock()->now().seconds();
        }

        // after the start of the lane change, it cannot be canceled.
        // to prevent the cancel we make the left lane changeability always
        // true.
        if (planning_module_status.state == PlanningModuleState::LANE_CHANGE) {
          T2_INFO << "Have started lane change to left before; must finish lane change to end";
          is_lane_changeable_left_lane = true;
        }
      }
    }
  } else {
    // Manage blinkers based on the map information.
    // The driver trigger signal has priority over the map blinker.
    // If the driver trigger is not in use, the turn_on_blinker contained in the
    // map file is used to control the vehicle blinker.
    if (ego_lane->lane().turn_on_blinker() == hdmap::Lane_BlinkerType_LEFT_BLINKER) {
      vehicle_signal.turn_signal = TurnSignal::TURN_LEFT;
    } else if (ego_lane->lane().turn_on_blinker() == hdmap::Lane_BlinkerType_RIGHT_BLINKER) {
      vehicle_signal.turn_signal = TurnSignal::TURN_RIGHT;
    } else {
      // If neither the driver trigger, neither the map commands a blinker,
      // TURN_NONE is sent.
      vehicle_signal.turn_signal = TurnSignal::TURN_NONE;
    }
  }

  // Compute the final lane changeable value.
  std::optional<double> winker_time_lane_changeability;  // foxbox plots nullopt as false
  std::optional<double> time_since_winker;
  // opt_trigger_start_time_ means that a the driver trigger is activated.
  if (lane_change_decider.opt_blinker_start_time) {
    time_since_winker =
        node.get_clock()->now().seconds() - *lane_change_decider.opt_blinker_start_time;
    winker_time_lane_changeability =
        time_since_winker.value() > config.lane_change_config.turn_signal_time;
  }
  const double is_lane_changeable =
      winker_time_lane_changeability && winker_time_lane_changeability.value() &&
      reference_line_map.count(ReferenceLineType::LANE_CHANGE_TARGET) &&
      LaneChangeability::DetermineLaneChangeabilityDirection(
          lane_change_decider.opt_trigger_last_decision_, is_lane_changeable_right_lane,
          is_lane_changeable_left_lane);

  const bool is_lane_changeable_distance =
      reference_line_map.count(ReferenceLineType::LANE_CHANGE_TARGET) &&
      LaneChangeability::DetermineLaneChangeabilityDirection(
          lane_change_decider.opt_trigger_last_decision_, is_lane_changeable_right_lane,
          is_lane_changeable_left_lane);

  // LOGGING
  // those variable are used for logging.
  // if there is no lane we display that the lane changeability is false.
  bool is_lane_changeable_right_lane_debug =
      is_lane_changeable_right_lane && ego_right_reference_line;
  bool is_lane_changeable_left_lane_debug = is_lane_changeable_left_lane && ego_left_reference_line;

  // Log Lane Changeability Data.
  LaneChangeLogging::LogLaneChangeability(
      lane_changeability_distances, is_lane_changeable_right_lane_debug,
      is_lane_changeable_left_lane_debug, is_lane_changeable, s_critical_right, s_critical_left,
      ego_lane_right_width, ego_lane_left_width, time_since_winker, winker_time_lane_changeability,
      safe_rear_right, safe_rear_left, safe_front_right, safe_front_left, safe_ego_front,
      left_lane_change_can_be_triggered, right_lane_change_can_be_triggered, left_trigger_geofenced,
      right_trigger_geofenced, left_lane_change_upper_limit, left_lane_change_lower_limit,
      right_lane_change_upper_limit, right_lane_change_lower_limit,
      output_trajectory.lane_change_data);

  // Log HMI information.
  // Those information are used to display the lance change status on the HMI
  // used by the driver. We add the lane change information to the
  // hmi_notification message.
  bool is_lane_change_triggered = false;
  if (config.use_long_winker) {
    // we verify that the winker is on and the lane changeability is true.
    // we use is_lane_changeable_distance and not is_lane_changeable because
    // is_lane_changeable contains also the initial blinking period. We want to
    // inform the driver of the start of the lane as soon as the lane change is
    // decided.
    is_lane_change_triggered =
        lane_change_decider.opt_blinker_start_time.has_value() && is_lane_changeable_distance;
  } else {
    // we verify that the winker is on. It is sufficient because if the winker
    // is on it means that the lane change has started.
    is_lane_change_triggered = lane_change_decider.opt_blinker_start_time.has_value();
  }
  LogHMI::LogLaneChangeHMI(planning_module_status, is_lane_change_triggered,
                           lane_change_decider.opt_trigger_last_decision_
                           //, hmi_notification
  );

  return std::tuple{is_lane_changeable, opt_lane_change_progress, obstacles_information};
}

// Replace the reference line obtained in the previous planning cycle with the
// one obtained in the current cycle. This is because the reference line centers
// around the ego vehicle's position for a specific cycle, including the
// surrounding lanes.
void ReplacePastReferenceLineWithCurrent(
    const std::list<ReferenceLine>& list_reference_lines,
    const std::list<route_lane_manager::RouteSegments>& list_route_segments,
    ReferenceLineAndRouteSegments& out_lane) {
  std::list<ReferenceLine>::const_iterator ref_line_iter;
  std::list<route_lane_manager::RouteSegments>::const_iterator segments_iter;
  for (ref_line_iter = list_reference_lines.begin(), segments_iter = list_route_segments.begin();
       ref_line_iter != list_reference_lines.end() && segments_iter != list_route_segments.end();
       ++ref_line_iter, ++segments_iter) {
    if (ref_line_iter->IsEqualTo(out_lane.reference_line)) {
      out_lane.reference_line = *ref_line_iter;
      out_lane.route_segments = *segments_iter;
      break;
    }
  }
}

void UpdateLaneChangeSourceAndDestinationReferenceLine(
    const PlanningModuleState& state, const std::list<ReferenceLine>& list_reference_lines,
    const std::list<route_lane_manager::RouteSegments>& list_route_segments,
    ReferenceLineAndRouteSegments& lc_source_lane,
    std::optional<ReferenceLineAndRouteSegments>& opt_lc_destination_lane) {
  switch (state) {
    case PlanningModuleState::LANE_CHANGE:
    case PlanningModuleState::LANE_CHANGE_CANCEL:
      ReplacePastReferenceLineWithCurrent(list_reference_lines, list_route_segments,
                                          lc_source_lane);
      if (opt_lc_destination_lane) {
        ReplacePastReferenceLineWithCurrent(list_reference_lines, list_route_segments,
                                            *opt_lc_destination_lane);
      }
      break;
    default:
      break;
  }
}

void SetLaneChangeSourceLaneBaseOnState(const PlanningModuleState& state,
                                        const ReferenceLineAndRouteSegments& current_driving_lane,
                                        ReferenceLineAndRouteSegments& lc_source_lane) {
  switch (state) {
    case PlanningModuleState::LANE_FOLLOW:
    case PlanningModuleState::LANE_CHANGE_COMPLETE: {
      lc_source_lane = current_driving_lane;
      break;
    }
    default: {
      if (lc_source_lane.empty()) {
        lc_source_lane = current_driving_lane;
      }
      break;
    }
  }
}

void AddCurrentObstaclesInformationToProto(
    const std::map<int, ObstacleInformation>& current_obstacles_information,
    ::planning_msgs::msg::Obstacles& obstacles_output) {
  for (auto const& [key, information] : current_obstacles_information) {
    // clang-format off
    obstacles_output.obstacle.emplace_back();
    ::planning_msgs::msg::Obstacle& obstacle_output = obstacles_output.obstacle.back();
    obstacle_output.id = key;
    obstacle_output.l_ego_reference_line = information.l_ego_reference_line;
    obstacle_output.l_in_ego_frame = information.l_in_ego_frame;
    obstacle_output.length = information.length;
    obstacle_output.width = information.width;
    obstacle_output.d_front = information.d_front.value_or(std::nan(""));
    obstacle_output.d_rear = information.d_rear.value_or(std::nan(""));
    obstacle_output.d_center = information.d_center;
    obstacle_output.linear_velocity = information.linear_velocity;
    obstacle_output.s = information.s;
    obstacle_output.right_edge_position = information.right_edge_position;
    obstacle_output.left_edge_position = information.left_edge_position;
    if(information.is_in_right_ego_lane){
      obstacle_output.is_in_right_ego_lane = information.is_in_right_ego_lane.value();
    }
    if(information.is_in_left_ego_lane){
      obstacle_output.is_in_left_ego_lane = information.is_in_left_ego_lane.value();
    }
    obstacle_output.is_in_ego_lane = information.is_in_ego_lane;
    obstacle_output.is_merging = information.is_merging;
    for (const auto& obstacle_point : information.obstacle_trajectory)
    {
      obstacle_output.obstacle_trajectory.emplace_back();
      ::planning_msgs::msg::ObstacleState& obstacle_state = obstacle_output.obstacle_trajectory.back();
      obstacle_state.t = obstacle_point.t;
      obstacle_state.s = obstacle_point.s;
      obstacle_state.s_front = obstacle_point.s_front;
      obstacle_state.l = obstacle_point.l;
      obstacle_state.v = obstacle_point.v;
      obstacle_state.is_in_ego_lane = obstacle_point.is_in_ego_lane;
    }
  }
}

}  // namespace t2::planning
