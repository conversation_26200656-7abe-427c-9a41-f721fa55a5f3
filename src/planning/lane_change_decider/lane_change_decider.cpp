// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "lane_change_decider.hpp"

#include "src/common/core/logging.hpp"  // T2_*

namespace t2::planning {

namespace lane_change_decider {

using lane_change_trigger::LaneChangeDirection;

bool LaneChangeDecider::IsCancelledByDriverTrigger(
    const std::optional<double>& opt_lane_change_progress,
    const LaneChangeDecision& decision_by_driver_trigger, const double& cancelation_threshold) {
  // Check for cancellation by driver trigger.
  // If the current lane change progress is less than the lane change
  // cancellation threshold. We verify if the lane change has not been canceled
  // by the driver.
  if (opt_lane_change_progress && opt_lane_change_progress.value() < cancelation_threshold) {
    if (opt_trigger_last_decision_.has_value() &&
        opt_trigger_last_decision_->lane_change_direction !=
            decision_by_driver_trigger.lane_change_direction) {
      return true;
    }
  }
  return false;
}

std::optional<ReferenceLineAndRouteSegments> LaneChangeDecider::UpdateDestinationLane(
    const PlanningModuleState& planning_state, const double& cancelation_threshold,
    const std::optional<double>& opt_lane_change_progress,
    const LaneChangeDecision& decision_by_driver_trigger,
    const ReferenceLineMap& reference_line_map,
    std::optional<ReferenceLineAndRouteSegments> opt_lc_destination_lane) {
  T2_INFO << LogUpdateDestinationLaneMessage(decision_by_driver_trigger, opt_lc_destination_lane,
                                             opt_lane_change_progress);
  // It is necessary to always update opt_lc_destination_lane variable when
  // doing a lane change. Otherwise, the opt_lc_destination_lane variable
  // contains an old reference line which is far from the current ego position.
  if (planning_state == PlanningModuleState::LANE_FOLLOW) {
    if (decision_by_driver_trigger.lane_change_direction == LaneChangeDirection::RIGHT &&
        reference_line_map.count(ReferenceLineType::RIGHT)) {
      opt_lc_destination_lane = reference_line_map.at(ReferenceLineType::RIGHT);
      opt_trigger_last_decision_ = decision_by_driver_trigger;
    } else if (decision_by_driver_trigger.lane_change_direction == LaneChangeDirection::LEFT &&
               reference_line_map.count(ReferenceLineType::LEFT)) {
      opt_lc_destination_lane = reference_line_map.at(ReferenceLineType::LEFT);
      opt_trigger_last_decision_ = decision_by_driver_trigger;
    } else {
      opt_lc_destination_lane.reset();
      opt_trigger_last_decision_.reset();
    }  // If the lane change is completed, we
       // reset the destination lane.
       // There will be no lane until the
       // winker position is changed.
  } else if (planning_state == PlanningModuleState::LANE_CHANGE_COMPLETE) {
    opt_lc_destination_lane.reset();
    // If the planning module status is canceled.
  } else if (planning_state == PlanningModuleState::LANE_CHANGE_CANCEL) {
    // If the lane change has been canceled and the winker is in the right or
    // left position. We update opt_trigger_last_decision_. If the lane
    // change is canceled by doing right->left or left -> right, this will block
    // a new lane change and requires the driver to put the winker in neutral
    // position.
    opt_lc_destination_lane.reset();
    if (decision_by_driver_trigger.lane_change_direction == LaneChangeDirection::NONE) {
      opt_trigger_last_decision_.reset();
    } else {
      opt_trigger_last_decision_ = decision_by_driver_trigger;
    }
  }

  // Verify if the lane change is canceled by the driver trigger.
  if (IsCancelledByDriverTrigger(opt_lane_change_progress, decision_by_driver_trigger,
                                 cancelation_threshold)) {
    opt_lc_destination_lane.reset();
  }

  T2_INFO << LogUpdateDestinationLaneMessage(decision_by_driver_trigger, opt_lc_destination_lane,
                                             opt_lane_change_progress);

  return opt_lc_destination_lane;
}

std::string LaneChangeDecider::LogUpdateDestinationLaneMessage(
    const LaneChangeDecision& decision_by_driver_trigger,
    const std::optional<ReferenceLineAndRouteSegments>& opt_lc_destination_lane,
    const std::optional<double>& opt_lane_change_progress) {
  const std::string lc_dest_lane_id =
      opt_lc_destination_lane
          ? (opt_lc_destination_lane->reference_line.GetLaneFromS(0.0)->id().id())
          : "nullopt";

  std::ostringstream os;
  os << "decision_by_driver_trigger.lane_change_direction = "
     << ToLaneChangeDirectionString(decision_by_driver_trigger.lane_change_direction)
     << ", last_decision.lane_change_direction = "
     << (opt_trigger_last_decision_
             ? ToLaneChangeDirectionString(opt_trigger_last_decision_->lane_change_direction)
             : "nullopt")
     << ", destination_lane = " << lc_dest_lane_id << ", lane_change_progress = "
     << (opt_lane_change_progress ? std::to_string(opt_lane_change_progress.value()) : "nullopt");
  return os.str();
};

}  // namespace lane_change_decider
}  // namespace t2::planning
