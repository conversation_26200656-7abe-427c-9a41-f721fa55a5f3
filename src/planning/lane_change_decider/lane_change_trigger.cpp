// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "lane_change_trigger.hpp"

namespace t2::planning {
namespace lane_change_trigger {

std::string ToLaneChangeDirectionString(LaneChangeDirection direction) {
  switch (direction) {
    case LaneChangeDirection::LEFT:
      return "LEFT";
    case LaneChangeDirection::RIGHT:
      return "RIGHT";
    default:
      return "NONE";
  }
}

}  // namespace lane_change_trigger
}  // namespace t2::planning
