// Copyright (c) 2025 T2 Inc. All rights reserved.

#pragma once

#include <planning_msgs/msg/planning_module_status.hpp>  // PlanningModuleState

#include "lane_change_decision.hpp"
#include "lane_change_trigger.hpp"
#include "src/planning/planning_macros.hpp"                // REGISTER_*_PLANNING_MSG
#include "src/planning/reference_line/reference_line.hpp"  // ReferenceLineMap

namespace t2::planning {

REGISTER_INTER_PLANNING_MSG(PlanningModuleState);

namespace lane_change_decider {

class LaneChangeDecider {
 public:
  LaneChangeDecider() = default;
  ~LaneChangeDecider() = default;

  std::optional<ReferenceLineAndRouteSegments> UpdateDestinationLane(
      const PlanningModuleState& planning_state, const double& cancelation_threshold,
      const std::optional<double>& opt_lane_change_progress,
      const LaneChangeDecision& decision_by_driver_trigger,
      const ReferenceLineMap& reference_line_map,
      std::optional<ReferenceLineAndRouteSegments> opt_lc_destination_lane);

  bool IsCancelledByDriverTrigger(const std::optional<double>& opt_lane_change_progress,
                                  const LaneChangeDecision& decision_by_driver_trigger,
                                  const double& cancelation_threshold);

  // Log
  std::string LogUpdateDestinationLaneMessage(
      const LaneChangeDecision& decision_by_driver_trigger,
      const std::optional<ReferenceLineAndRouteSegments>& opt_destination_lane,
      const std::optional<double>& opt_lane_change_progress);

  std::optional<LaneChangeDecision> opt_trigger_last_decision_;
  std::optional<double> opt_trigger_start_time_;  // The moment when that trigger started.
  std::optional<double> opt_blinker_start_time;   // The moment when the blinker starts blinking.
};

}  // namespace lane_change_decider
}  // namespace t2::planning
