// Copyright (c) 2025 T2 Inc. All rights reserved.

/**
 * @file lane_change.h
 *
 * @brief
 */
#pragma once

#include <list>
#include <utility>

#include <common_msgs/msg/engage_advice.hpp>                  // EngageAdvice, Advice
#include <common_msgs/msg/vehicle_signal.hpp>                 // VehicleSignal, TurnSignal
#include <planning_msgs/msg/planning_module_status.hpp>       // PlanningModuleStatus
#include <planning_msgs/msg/planning_request.hpp>             // PlanningCommand
#include <planning_trajectory_msgs/msg/sl_point.hpp>          // SLPoint
#include <planning_trajectory_msgs/msg/trajectory_point.hpp>  // TrajectoryPoint
#include <rclcpp/rclcpp.hpp>                                  // rclcpp::Clock

#include "lane_changeability.hpp"
#include "logging/log_hmi.hpp"
#include "logging/log_lane_changeability.hpp"
#include "src/planning/common/intention_task_data.hpp"
#include "src/planning/common/obstacle.hpp"  // ObstacleInformation
#include "src/planning/common/planning_vehicle_state/planning_vehicle_state.hpp"
#include "src/planning/config/planner_config.hpp"
#include "src/planning/config/planning_config.hpp"  // PlanningConfiguration
#include "src/planning/lane_change_decider/driver_trigger/driver_trigger.hpp"
#include "src/planning/lane_change_decider/lane_change_decider.hpp"
#include "src/planning/lane_change_decider/lane_change_decision.hpp"
#include "src/planning/planning_common.hpp"
#include "src/planning/reference_line/reference_line_provider.hpp"  // route_lane_manager::RouteSegments

namespace t2::planning {

REGISTER_INTER_PLANNING_MSG(PlanningModuleStatus);
REGISTER_INTER_PLANNING_MSG(PlanningCommand);
REGISTER_INTER_TRAJECTORY_MSG(TrajectoryPoint);
REGISTER_INTER_TRAJECTORY_MSG(SLPoint);
REGISTER_INTER_COMMON_MSG(TurnSignal);
REGISTER_INTER_COMMON_MSG(Advice);

// using t2::hmi::HMINotification;

std::tuple<bool, std::optional<double>, std::map<int, ObstacleInformation>> LaneChange(
    const PlanningConfiguration& config, const PlannerConfig& planner_config,
    std::optional<ReferenceLineAndRouteSegments>& opt_lc_destination_lane,
    const std::list<route_lane_manager::RouteSegments>& list_route_segments,
    const std::list<ReferenceLine>& list_reference_lines,
    PlanningModuleStatus& planning_module_status,
    const ReferenceLineAndRouteSegments& current_driving_lane,
    ReferenceLineAndRouteSegments& lc_source_lane, ReferenceLineMap& reference_line_map,
    const PlanningVehicleState& aligned_vehicle_state, const Advice& engage_advice,
    const TurnSignal& turn_signal, const PlanningCommand& planning_command,
    lane_change_decider::LaneChangeDecision& lane_change_decision,
    lane_change_trigger::DriverTrigger& lane_change_driver_trigger,
    std::shared_ptr<IntentionTaskData>& last_trajectory_intention_ptr,
    lane_change_decider::LaneChangeDecider& lane_change_decider,
    const TrajectoryPoint& planning_start_point, ADCTrajectory& output_trajectory,
    const PlanningVehicleState& projected_vehicle_state,
    const std::map<int, Obstacle>& obstacle_map, rclcpp::Node& node,
    //, HMINotification& hmi_notification,
    hdmap::LaneInfoConstPtr ego_lane);

void UpdateLaneChangeSourceAndDestinationReferenceLine(
    const PlanningModuleState& state, const std::list<ReferenceLine>& list_reference_lines,
    const std::list<route_lane_manager::RouteSegments>& list_route_segments,
    ReferenceLineAndRouteSegments& lc_source_lane,
    std::optional<ReferenceLineAndRouteSegments>& opt_lc_destination_lane);

std::optional<double> CalculateLaneChangeProgress(const PlanningVehicleState& vehicle_state,
                                                  const ReferenceLine& lc_source_ref_line,
                                                  const ReferenceLine& lc_destination_ref_line);

void ReplacePastReferenceLineWithCurrent(
    const std::list<ReferenceLine>& list_reference_lines,
    const std::list<route_lane_manager::RouteSegments>& list_route_segments,
    ReferenceLineAndRouteSegments& out_lane);

void SetLaneChangeSourceLaneBaseOnState(const PlanningModuleState& state,
                                        const ReferenceLineAndRouteSegments& current_driving_lane,
                                        ReferenceLineAndRouteSegments& lc_source_lane);

}  // namespace t2::planning
