// Copyright (c) 2025 T2 Inc. All rights reserved.

/**
 * @file log_lane_changeability.h
 *
 * @brief
 */
#pragma once

#include <planning_msgs/msg/lane_change_data.hpp>  // LaneChangeData

#include "src/planning/lane_change_decider/lane_changeability.hpp"
#include "src/planning/planning_macros.hpp"  // REGISTER_*_PLANNING_MSG

namespace t2::planning {

REGISTER_INTER_PLANNING_MSG(LaneChangeData);

namespace LaneChangeLogging {

/**
 * @class LogLaneChangeability
 *
 * @brief This method adds the Lane Changeability to the planning LaneChangeData
 * proto. It logs the lane changeability on the right and left lane, the s
 * critical for the right and left lane and the width of the ego lane.
 */
void LogLaneChangeability(
    const LaneChangeability::LaneChangeabilityInfos& lane_changeability_distances,
    const bool& is_lane_changeable_right, const bool& is_lane_changeable_left,
    const bool& is_lane_changeable, const std::optional<double>& opt_s_critical_right,
    const std::optional<double>& opt_s_critical_left,
    const std::optional<double>& opt_ego_lane_right_width,
    const std::optional<double>& opt_ego_lane_left_width,
    const std::optional<double>& opt_time_since_winker,
    const std::optional<double>& opt_winker_time_lane_changeability,
    const std::optional<double>& opt_d_safe_rear_right,
    const std::optional<double>& opt_d_safe_rear_left,
    const std::optional<double>& opt_d_safe_front_right,
    const std::optional<double>& opt_d_safe_front_left, const std::optional<double>& opt_d_safe_ego,
    const bool left_lc_triggerable, const bool right_lc_triggerable,
    const bool left_trigger_geofenced, const bool right_trigger_geofenced,
    const double left_lane_change_upper_limit, const double left_lane_change_lower_limit,
    const double right_lane_change_upper_limit, const double right_lane_change_lower_limit,
    LaneChangeData& lane_change_data_output);

}  // namespace LaneChangeLogging
}  // namespace t2::planning
