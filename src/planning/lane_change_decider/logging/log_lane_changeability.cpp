// Copyright (c) 2025 T2 Inc. All rights reserved.

#include "log_lane_changeability.hpp"

namespace t2::planning {
namespace LaneChangeLogging {

void LogLaneChangeability(
    const LaneChangeability::LaneChangeabilityInfos& lane_changeability_distances,
    const bool& is_lane_changeable_right, const bool& is_lane_changeable_left,
    const bool& is_lane_changeable, const std::optional<double>& opt_s_critical_right,
    const std::optional<double>& opt_s_critical_left,
    const std::optional<double>& opt_ego_lane_right_width,
    const std::optional<double>& opt_ego_lane_left_width,
    const std::optional<double>& opt_time_since_winker,
    const std::optional<double>& opt_winker_time_lane_changeability,
    const std::optional<double>& opt_d_safe_rear_right,
    const std::optional<double>& opt_d_safe_rear_left,
    const std::optional<double>& opt_d_safe_front_right,
    const std::optional<double>& opt_d_safe_front_left, const std::optional<double>& opt_d_safe_ego,
    const bool left_lc_triggerable, const bool right_lc_triggerable,
    const bool left_trigger_geofenced, const bool right_trigger_geofenced,
    const double left_lane_change_upper_limit, const double left_lane_change_lower_limit,
    const double right_lane_change_upper_limit, const double right_lane_change_lower_limit,
    LaneChangeData& lane_change_data_output) {
  const auto& left_lane = lane_changeability_distances.left_lane;
  const auto& right_lane = lane_changeability_distances.right_lane;
  const auto& ego_lane = lane_changeability_distances.ego_lane;

  const double d_front_right = right_lane->d_front.value_or(std::nan(""));
  const double d_front_left = left_lane->d_front.value_or(std::nan(""));
  const double d_front_ego = ego_lane->d_front.value_or(std::nan(""));
  const double d_rear_right = right_lane->d_rear.value_or(std::nan(""));
  const double d_rear_left = left_lane->d_rear.value_or(std::nan(""));
  const double d_rear_ego = ego_lane->d_rear.value_or(std::nan(""));
  const double d_center_right = right_lane->d_center.value_or(std::nan(""));
  const double d_center_left = left_lane->d_center.value_or(std::nan(""));
  const double d_center_ego = ego_lane->d_center.value_or(std::nan(""));
  const double v_front_right = right_lane->v_front.value_or(std::nan(""));
  const double v_front_left = left_lane->v_front.value_or(std::nan(""));
  const double v_front_ego = ego_lane->v_front.value_or(std::nan(""));
  const double v_rear_right = right_lane->v_rear.value_or(std::nan(""));
  const double v_rear_left = left_lane->v_rear.value_or(std::nan(""));
  const double v_rear_ego = ego_lane->v_rear.value_or(std::nan(""));

  const double s_critical_right = opt_s_critical_right.value_or(std::nan(""));
  const double s_critical_left = opt_s_critical_left.value_or(std::nan(""));
  const double d_safe_rear_right = opt_d_safe_rear_right.value_or(std::nan(""));
  const double d_safe_rear_left = opt_d_safe_rear_left.value_or(std::nan(""));
  const double d_safe_front_right = opt_d_safe_front_right.value_or(std::nan(""));
  const double d_safe_front_left = opt_d_safe_front_left.value_or(std::nan(""));
  const double d_safe_ego = opt_d_safe_ego.value_or(std::nan(""));

  const double ego_lane_right_width = opt_ego_lane_right_width.value_or(std::nan(""));
  const double ego_lane_left_width = opt_ego_lane_left_width.value_or(std::nan(""));
  const double time_since_winker = opt_time_since_winker.value_or(std::nan(""));
  const double winker_time_lane_changeability =
      opt_winker_time_lane_changeability.value_or(std::nan(""));

  if (!left_trigger_geofenced) {
    T2_INFO << std::boolalpha << "left_lc_triggerable=" << left_lc_triggerable
            << ", is_lane_changeable_left=" << is_lane_changeable_left
            << ", is_lane_changeable=" << is_lane_changeable << ", d_front_left=" << d_front_left
            << ", d_center_left=" << d_center_left << ", d_rear_left=" << d_rear_left
            << ", s_critical_left=" << s_critical_left
            << ", d_safe_front_left=" << d_safe_front_left
            << ", d_safe_rear_left=" << d_safe_rear_left;
  }

  if (!right_trigger_geofenced) {
    T2_INFO << std::boolalpha << "right_lc_triggerable=" << right_lc_triggerable
            << ", is_lane_changeable_right=" << is_lane_changeable_right
            << ", is_lane_changeable=" << is_lane_changeable << ", d_front_right=" << d_front_right
            << ", d_center_right=" << d_center_right << ", d_rear_right=" << d_rear_right
            << ", s_critical_right=" << s_critical_right
            << ", d_safe_front_right=" << d_safe_front_right
            << ", d_safe_rear_right=" << d_safe_rear_right;
  }

  // clang-format off
  lane_change_data_output.d_front_right = d_front_right;
  lane_change_data_output.d_front_left = d_front_left;
  lane_change_data_output.d_front_ego = d_front_ego;
  lane_change_data_output.d_rear_right = d_rear_right;
  lane_change_data_output.d_rear_left = d_rear_left;
  lane_change_data_output.d_rear_ego = d_rear_ego;
  lane_change_data_output.d_center_right = d_center_right;
  lane_change_data_output.d_center_left = d_center_left;
  lane_change_data_output.d_center_ego = d_center_ego;
  lane_change_data_output.v_front_right = v_front_right;
  lane_change_data_output.v_front_left = v_front_left;
  lane_change_data_output.v_front_ego = v_front_ego;
  lane_change_data_output.v_rear_right = v_rear_right;
  lane_change_data_output.v_rear_left = v_rear_left;
  lane_change_data_output.v_rear_ego = v_rear_ego;

  lane_change_data_output.s_critical_right = s_critical_right;
  lane_change_data_output.s_critical_left = s_critical_left;
  lane_change_data_output.d_safe_rear_right = d_safe_rear_right;
  lane_change_data_output.d_safe_rear_left = d_safe_rear_left;
  lane_change_data_output.d_safe_front_right = d_safe_front_right;
  lane_change_data_output.d_safe_front_left = d_safe_front_left;
  lane_change_data_output.d_safe_ego = d_safe_ego;

  lane_change_data_output.ego_lane_right_width = ego_lane_right_width;
  lane_change_data_output.ego_lane_left_width = ego_lane_left_width;
  lane_change_data_output.time_since_winker = time_since_winker;
  lane_change_data_output.winker_time_lane_changeability = winker_time_lane_changeability;

  lane_change_data_output.lane_changeable_right = is_lane_changeable_right;
  lane_change_data_output.lane_changeable_left = is_lane_changeable_left;
  lane_change_data_output.lane_changeable_result = is_lane_changeable;

  auto& trigger_information = lane_change_data_output.trigger_information;
  trigger_information.left_lc_triggerable = left_lc_triggerable;
  trigger_information.right_lc_triggerable = right_lc_triggerable;
  trigger_information.left_trigger_geofenced = left_trigger_geofenced;
  trigger_information.right_trigger_geofenced = right_trigger_geofenced;
  trigger_information.left_lane_change_upper_limit = left_lane_change_upper_limit;
  trigger_information.left_lane_change_lower_limit = left_lane_change_lower_limit;
  trigger_information.right_lane_change_upper_limit = right_lane_change_upper_limit;
  trigger_information.right_lane_change_lower_limit = right_lane_change_lower_limit;
}

}  // namespace LaneChangeLogging
}  // namespace t2::planning
