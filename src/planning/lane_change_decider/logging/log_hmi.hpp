// Copyright (c) 2025 T2 Inc. All rights reserved.
// Copyright 2025 T2 Inc.
/**
 * @file log_hmi.h
 *
 * @brief
 */
#pragma once

#include <planning_msgs/msg/planning_module_status.hpp>  // PlanningModuleStatus

#include "src/planning/lane_change_decider/lane_change_decision.hpp"
#include "src/planning/planning_macros.hpp"  // REGISTER_*_PLANNING_MSG

namespace t2::planning {

REGISTER_INTER_PLANNING_MSG(PlanningModuleState);
REGISTER_INTER_PLANNING_MSG(PlanningModuleStatus);

namespace LogHMI {

// using t2::ad_monitor::ADEvent;
// using t2::ad_monitor::Direction;
// using t2::hmi::HMINotification;

/**
 * @class LogHMI
 *
 * @brief
 */
void LogLaneChangeHMI(const PlanningModuleStatus& planning_status,
                      const bool is_lane_change_triggered,
                      const std::optional<lane_change_decider::LaneChangeDecision>& trigger_decision
                      // to-do
                      // ,
                      // HMINotification& hmi_notification
);

}  // namespace LogHMI
}  // namespace t2::planning
