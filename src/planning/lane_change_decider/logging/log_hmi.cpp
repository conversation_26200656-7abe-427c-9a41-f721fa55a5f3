// Copyright (c) 2025 T2 Inc. All rights reserved.
// Copyright 2025 T2 Inc.
/**
 * @file log_hmi.cc
 *
 * @brief
 */
#include "log_hmi.hpp"

namespace t2::planning {

namespace LogHMI {

void LogLaneChangeHMI(const PlanningModuleStatus& planning_status,
                      const bool is_lane_change_triggered,
                      const std::optional<lane_change_decider::LaneChangeDecision>& trigger_decision
                      // , HMINotification& hmi_notification
) {
  if (planning_status.state == PlanningModuleState::LANE_CHANGE_COMPLETE) {
    // hmi_notification.clear_events();
    // t2::ad_monitor::ADEvent* event = hmi_notification.add_events();
    // event->set_type(t2::ad_monitor::AD_EVENT_TYPE_LANE_CHANGE_COMPLETE);
  } else if (planning_status.state == PlanningModuleState::LANE_CHANGE_CANCEL) {
    //   if ((hmi_notification.events_size() == 1) &&
    //       (hmi_notification.events(0).type() == t2::ad_monitor::AD_EVENT_TYPE_TAKEOVER_REQUEST))
    //       {
    //   } else {
    //     hmi_notification.clear_events();
    //     t2::ad_monitor::ADEvent* event = hmi_notification.add_events();
    //     event->set_type(t2::ad_monitor::AD_EVENT_TYPE_LANE_CHANGE_CANCEL);
    //   }
    // } else {
    //   if (is_lane_change_triggered) {
    //     hmi_notification.clear_events();
    //     t2::ad_monitor::ADEvent* event = hmi_notification.add_events();
    //     event->set_type(t2::ad_monitor::AD_EVENT_TYPE_LANE_CHANGE_TRIGGERED);
    //     if (trigger_decision && trigger_decision->lane_change_direction ==
    //                                 lane_change_trigger::LaneChangeDirection::RIGHT) {
    //       event->set_direction(t2::ad_monitor::DIRECTION_LEFT_TO_RIGHT);
    //     } else if (trigger_decision && trigger_decision->lane_change_direction ==
    //                                        lane_change_trigger::LaneChangeDirection::LEFT) {
    //       event->set_direction(t2::ad_monitor::DIRECTION_RIGHT_TO_LEFT);
    //     }
    //   }
  }
}

}  // namespace LogHMI
}  // namespace t2::planning
