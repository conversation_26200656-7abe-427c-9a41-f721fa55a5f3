// Copyright (c) 2025 T2 Inc. All rights reserved.

/**
 * @file lane_changeability.h
 *
 * @brief
 */
#pragma once

#include <map>
#include <optional>
#include <utility>

#include "lane_change_decision.hpp"
#include "src/planning/common/obstacle.hpp"  // ObstacleInformation
#include "src/planning/config/planning_config.hpp"

namespace t2::planning {
namespace LaneChangeability {

struct LaneChangeabilityDistances {
  std::optional<double> d_front;
  std::optional<double> d_rear;
  std::optional<double> d_center;
  std::optional<double> v_front;
  std::optional<double> v_rear;
};

struct LaneChangeabilityInfos {
  std::optional<LaneChangeabilityDistances> right_lane;
  std::optional<LaneChangeabilityDistances> left_lane;
  std::optional<LaneChangeabilityDistances> ego_lane;
};

/**
 * @class IsLaneChangeable
 *
 * @brief This method evaluate the lane changeability considering the distance
 * with the surrouding obstacles and their speed.
 *
 * @param d_front is the distance with the front vehicle
 * @param d_rear is the distance with the rear obstacle
 * @param s_critical is the critical s for doing the lane change
 * @param safe_rear_d is the safe distance from the rear vehicle that define if
 * we can do the lane change
 * @param min_d_front is the minimum front distance (constant)
 * @param min_d_rear is the minimum rear distance (constant)
 * @param use_s_critical_for_lane_change define how we compute the s_critical
 * @param use_d_safe_rear_for_lane_change define how we compute the s_critical
 */
bool IsLaneChangeable(
    const std::optional<double>& d_front, const std::optional<double>& d_rear,
    const std::optional<double>& d_front_ego, const std::optional<double>& s_critical,
    const std::optional<double>& safe_rear_d, const std::optional<double>& safe_front_d,
    const std::optional<double>& safe_ego_d, const double& min_d_front, const double& min_d_rear,
    const bool& use_s_critical_for_lane_change, const bool& use_d_safe_rear_for_lane_change,
    const bool& use_d_safe_front_for_lane_change, const bool& use_d_safe_ego_for_lane_change);

/**
 * @class DetermineLaneChangeabilityDirection
 *
 * @brief This method determines which lane changeability value use wether the
 * right or the left based on the trigger decision.
 */
bool DetermineLaneChangeabilityDirection(
    const std::optional<lane_change_decider::LaneChangeDecision>& trigger_decision,
    const bool& is_lane_changeable_right_lane, const bool& is_lane_changeable_left_lane);

/**
 * @class DetermineLaneChangeabilityInformation
 *
 * @brief This method gather the information necessary to determine the lane
 * changeability.
 */
LaneChangeabilityInfos DetermineLaneChangeabilityInformation(
    const std::map<int, ObstacleInformation>& current_obstacles_information);

/**
 * @class GetSCriticalToRearObstacle
 *
 * @brief This method compute the s critical for a rear obstacle.
 */
std::optional<double> GetSCriticalToRearObstacle(
    const double ego_v, const std::optional<double> back_obs_v,
    LaneChangeConfig::LaneChangeableParameters lane_changeable_parameters);

/**
 * @brief Calculates the safe distance from a rear obstacle
 *
 * This function is used when the ego vehicle wants to perform a lane change. In
 * the lane where it wants to merge there is a vehicle positioned behind us. The
 * result is the safe distance from the rear vehicle when the lane change can
 * start
 *
 * @param ego_v is the ego vehicle speed
 * @param rear_obs_V is the speed of the rear vehicle.
 * @param lc_params is the variable where the configuration parameters are
 * stored
 *
 *
 * @return an optional<double> indicating the minimum safe distance. If the rear
 * vehicle is not detected the optional will be false
 */
std::optional<double> GetDSafeRear(
    const double ego_v, const std::optional<double> rear_obs_v,
    const LaneChangeConfig::LaneChangeableParameters::DSafeRear& lc_params);

/**
 * @brief Calculates the safe distance from a front obstacle
 *
 * This function is used when the ego vehicle wants to perform a lane change. In
 * the lane where it wants to merge there is a vehicle positioned in front of
 * us. The result is the safe distance from the front vehicle when the lane
 * change can start
 *
 * @param ego_v is the ego vehicle speed
 * @param front_obs_V is the speed of the front vehicle.
 * @param lc_params is the variable where the configuration parameters are
 * stored
 *
 *
 * @return an optional<double> indicating the minimum safe distance. If the
 * front vehicle is not detected the optional will be false
 */
std::optional<double> GetDSafeFront(
    const double ego_v, const std::optional<double> front_obs_v,
    const LaneChangeConfig::LaneChangeableParameters::DSafeFront& lc_params);

}  // namespace LaneChangeability
}  // namespace t2::planning
