// Copyright (c) 2025 T2 Inc. All rights reserved.
#include "lane_changeability.hpp"

namespace t2::planning {
namespace LaneChangeability {

bool IsLaneChangeable(
    const std::optional<double>& d_front, const std::optional<double>& d_rear,
    const std::optional<double>& d_front_ego, const std::optional<double>& s_critical,
    const std::optional<double>& safe_rear_d, const std::optional<double>& safe_front_d,
    const std::optional<double>& safe_ego_d, const double& min_d_front, const double& min_d_rear,
    const bool& use_s_critical_for_lane_change, const bool& use_d_safe_rear_for_lane_change,
    const bool& use_d_safe_front_for_lane_change, const bool& use_d_safe_ego_for_lane_change) {
  // Verify the changeability regarding the front obstacle.
  bool is_lane_changeable_front = true;
  // Verify the changeability regarding the rear obstacle.
  bool is_lane_changeable_rear = true;
  // Verify the changeability regarding the front obstacle in ego lane.
  bool is_lane_changeable_front_ego = true;

  // If S critical is used the min_d_rear is given by s critical.
  auto _min_d_rear = min_d_rear;

  if (use_s_critical_for_lane_change && s_critical) {
    _min_d_rear = s_critical.value();
  }

  if (use_d_safe_rear_for_lane_change && safe_rear_d) {
    _min_d_rear = safe_rear_d.value();
  }

  auto _min_d_front = min_d_front;

  if (use_d_safe_front_for_lane_change && safe_front_d) {
    _min_d_front = safe_front_d.value();
  }

  // If there is a front obstacle verify if it is not too close.
  if (d_front) is_lane_changeable_front = (d_front > _min_d_front);
  // If there is a rear obstacle, verify if it is not too close.
  if (d_rear) is_lane_changeable_rear = (d_rear > _min_d_rear);
  // If there is a front vehicle in the ego lane, verify if it is not too close.
  if (d_front_ego && use_d_safe_ego_for_lane_change)
    is_lane_changeable_front_ego = (d_front_ego > safe_ego_d);

  // If both condition are true, the lane change is allowed.
  return (is_lane_changeable_front && is_lane_changeable_rear && is_lane_changeable_front_ego);
}

LaneChangeabilityInfos DetermineLaneChangeabilityInformation(
    const std::map<int, ObstacleInformation>& current_obstacles_information) {
  LaneChangeabilityDistances lane_changeability_information_right;
  LaneChangeabilityDistances lane_changeability_information_left;
  LaneChangeabilityDistances lane_changeability_information_ego;
  for (auto const& [key, information] : current_obstacles_information) {
    // If the obstacle is in the right lane.
    if (information.is_in_right_ego_lane) {
      // If the obstacle is in front of the ego vehicle.
      if (information.d_front) {
        // We determine which obstacle is the closest to the ego vehicle.
        if (lane_changeability_information_right.d_front) {
          if (information.d_front < lane_changeability_information_right.d_front.value()) {
            lane_changeability_information_right.d_front = information.d_front;
            lane_changeability_information_right.v_front = information.linear_velocity;
          }
        } else {
          lane_changeability_information_right.d_front = information.d_front;
          lane_changeability_information_right.v_front = information.linear_velocity;
        }
      }
      // If the obstacle is is behind the ego vehicle.
      if (information.d_rear) {
        // We determine which obstacle is the closest to the ego vehicle.
        if (lane_changeability_information_right.d_rear) {
          if (information.d_rear < lane_changeability_information_right.d_rear.value()) {
            lane_changeability_information_right.d_rear = information.d_rear;
            lane_changeability_information_right.v_rear = information.linear_velocity;
          }
        } else {
          lane_changeability_information_right.d_rear = information.d_rear;
          lane_changeability_information_right.v_rear = information.linear_velocity;
        }
      }
      // We determine which obstacle is the closest to the ego vehicle.
      if (lane_changeability_information_right.d_center) {
        if (abs(information.d_center) <
            abs(lane_changeability_information_right.d_center.value())) {
          lane_changeability_information_right.d_center = information.d_center;
        }
      } else {
        lane_changeability_information_right.d_center = information.d_center;
      }
    }

    // If the obstacle is in the left lane.
    if (information.is_in_left_ego_lane) {
      // If the obstacle is in front of the ego vehicle.
      if (information.d_front) {
        // We determine which obstacle is the closest to the ego vehicle.
        if (lane_changeability_information_left.d_front) {
          if (information.d_front < lane_changeability_information_left.d_front.value()) {
            lane_changeability_information_left.d_front = information.d_front;
            lane_changeability_information_left.v_front = information.linear_velocity;
          }
        } else {
          lane_changeability_information_left.d_front = information.d_front;
          lane_changeability_information_left.v_front = information.linear_velocity;
        }
      }
      // If the obstacle is is behind the ego vehicle.
      if (information.d_rear) {
        // We determine which obstacle is the closest to the ego vehicle.
        if (lane_changeability_information_left.d_rear) {
          if (information.d_rear < lane_changeability_information_left.d_rear.value()) {
            lane_changeability_information_left.d_rear = information.d_rear;
            lane_changeability_information_left.v_rear = information.linear_velocity;
          }
        } else {
          lane_changeability_information_left.d_rear = information.d_rear;
          lane_changeability_information_left.v_rear = information.linear_velocity;
        }
      }
      // We determine which obstacle is the closest to the ego vehicle.
      if (lane_changeability_information_left.d_center) {
        if (abs(information.d_center) < abs(lane_changeability_information_left.d_center.value())) {
          lane_changeability_information_left.d_center = information.d_center;
        }
      } else {
        lane_changeability_information_left.d_center = information.d_center;
      }
    }

    // If the obstacle is in the ego lane.
    if (information.is_in_ego_lane) {
      // If the obstacle is in front of the ego vehicle.
      if (information.d_front) {
        // We determine which obstacle is the closest to the ego vehicle.
        if (lane_changeability_information_ego.d_front) {
          if (information.d_front < lane_changeability_information_ego.d_front.value()) {
            lane_changeability_information_ego.d_front = information.d_front;
            lane_changeability_information_ego.v_front = information.linear_velocity;
          }
        } else {
          lane_changeability_information_ego.d_front = information.d_front;
          lane_changeability_information_ego.v_front = information.linear_velocity;
        }
      }
      // If the obstacle is is behind the ego vehicle.
      if (information.d_rear) {
        // We determine which obstacle is the closest to the ego vehicle.
        if (lane_changeability_information_ego.d_rear) {
          if (information.d_rear < lane_changeability_information_ego.d_rear.value()) {
            lane_changeability_information_ego.d_rear = information.d_rear;
            lane_changeability_information_ego.v_rear = information.linear_velocity;
          }
        } else {
          lane_changeability_information_ego.d_rear = information.d_rear;
          lane_changeability_information_ego.v_rear = information.linear_velocity;
        }
      }
      // We determine which obstacle is the closest to the ego vehicle.
      if (lane_changeability_information_ego.d_center) {
        if (abs(information.d_center) < abs(lane_changeability_information_ego.d_center.value())) {
          lane_changeability_information_ego.d_center = information.d_center;
        }
      } else {
        lane_changeability_information_ego.d_center = information.d_center;
      }
    }
  }

  // If there is an obstacle side by side with the ego vehicle, there will be no
  // d_front nor d_rear. In that case we set those information to 0.0 to
  // prevent any lane change.
  if (lane_changeability_information_right.d_center) {
    if (abs(lane_changeability_information_right.d_center.value()) < 20.0) {
      lane_changeability_information_right.d_front = 0.0;
      lane_changeability_information_right.d_rear = 0.0;
      lane_changeability_information_right.v_front = std::nullopt;
      lane_changeability_information_right.v_rear = std::nullopt;
    }
  }
  if (lane_changeability_information_left.d_center) {
    if (abs(lane_changeability_information_left.d_center.value()) < 20.0) {
      lane_changeability_information_left.d_front = 0.0;
      lane_changeability_information_left.d_rear = 0.0;
      lane_changeability_information_left.v_front = std::nullopt;
      lane_changeability_information_left.v_rear = std::nullopt;
    }
  }

  // output
  LaneChangeabilityInfos lane_changeability_information;
  lane_changeability_information.left_lane = lane_changeability_information_left;
  lane_changeability_information.right_lane = lane_changeability_information_right;
  lane_changeability_information.ego_lane = lane_changeability_information_ego;

  return lane_changeability_information;
}

bool DetermineLaneChangeabilityDirection(
    const std::optional<lane_change_decider::LaneChangeDecision>& trigger_decision,
    const bool& is_lane_changeable_right_lane, const bool& is_lane_changeable_left_lane) {
  auto is_lane_changeable = false;

  // If the trigger requests a lane change to the right lane, we use
  // the is_lane_changeable_right_lane, if the trigger requests a lane change to
  // the left lane, we use is_lane_changeable_left_lane.
  if (trigger_decision &&
      trigger_decision->lane_change_direction == lane_change_trigger::LaneChangeDirection::RIGHT) {
    is_lane_changeable = is_lane_changeable_right_lane;
  } else if (trigger_decision && trigger_decision->lane_change_direction ==
                                     lane_change_trigger::LaneChangeDirection::LEFT) {
    is_lane_changeable = is_lane_changeable_left_lane;
  }
  return is_lane_changeable;
}

std::optional<double> GetSCriticalToRearObstacle(
    const double ego_v, const std::optional<double> back_obs_v,
    LaneChangeConfig::LaneChangeableParameters lane_changeable_parameters) {
  std::optional<double> s_critical;
  if (back_obs_v) {
    if (ego_v >= back_obs_v.value()) {
      return back_obs_v.value() * lane_changeable_parameters.target_time_headway;
    }
    const double relative_v = back_obs_v.value() - ego_v;
    const double move_distance_before_braking =
        relative_v * lane_changeable_parameters.time_to_start_deceleration;
    const double braking_distance =
        relative_v * relative_v / (lane_changeable_parameters.obstacle_deceleration_strength * 2);
    const double target_inter_vehicle_distance =
        ego_v * lane_changeable_parameters.target_time_headway;
    s_critical = move_distance_before_braking + braking_distance + target_inter_vehicle_distance;
  }
  return s_critical;
}

std::optional<double> GetDSafeRear(
    const double ego_v, const std::optional<double> rear_obs_v,
    const LaneChangeConfig::LaneChangeableParameters::DSafeRear& lc_params) {
  std::optional<double> d_safe_rear;
  if (rear_obs_v) {
    double relative_v = rear_obs_v.value() - ego_v;
    d_safe_rear = lc_params.d_min;
    if (relative_v >= 0.0) {
      d_safe_rear = lc_params.d_min + lc_params.reaction_time * (relative_v) -
                    ((relative_v * relative_v) / (2 * lc_params.acc_min_rear));
    }
  }
  return d_safe_rear;
}

std::optional<double> GetDSafeFront(
    const double ego_v, const std::optional<double> front_obs_v,
    const LaneChangeConfig::LaneChangeableParameters::DSafeFront& lc_params) {
  std::optional<double> d_safe_front;
  if (front_obs_v) {
    double relative_v = ego_v - front_obs_v.value();
    d_safe_front = lc_params.d_min;
    if (relative_v >= 0.0) {
      d_safe_front = lc_params.d_min + lc_params.reaction_time * (relative_v) -
                     ((relative_v * relative_v) / (2 * lc_params.acc_min_ego));
    }
  }
  return d_safe_front;
}

}  // namespace LaneChangeability
}  // namespace t2::planning
