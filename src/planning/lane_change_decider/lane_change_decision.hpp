// Copyright (c) 2025 T2 Inc. All rights reserved.

#pragma once

#include "lane_change_trigger.hpp"

namespace t2::planning {
namespace lane_change_decider {

struct LaneChangeDecision {
  enum class Status {
    UNDEFINED = 0,
    OK = 1,
    UNSUPPORTED_CASE = 2,
    CHECK_EGO_CLOSE_TO_MERGE_POINT_FAILED = 3,
    ABORT_LANE_CHANGE_ATTEMPT_TOO_LONG_SIGNAL = 4
  };

  enum class TriggerType { UNDEFINED = 0, DRIVER = 1, INCREASE_DECREASE = 2 };

  Status status = Status::UNDEFINED;
  lane_change_trigger::LaneChangeDirection lane_change_direction =
      lane_change_trigger::LaneChangeDirection::NONE;
  lane_change_trigger::IncreaseDecreaseTrigger lane_change_trigger;
  TriggerType trigger_type = TriggerType::UNDEFINED;
};

}  // namespace lane_change_decider
}  // namespace t2::planning
