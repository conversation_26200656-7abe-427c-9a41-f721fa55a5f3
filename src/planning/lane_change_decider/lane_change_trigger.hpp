// Copyright (c) 2025 T2 Inc. All rights reserved.

#pragma once

#include "src/map/proto/map_id.pb.h"  // ::t2::map::hdmap::Id

namespace t2::planning {

namespace hdmap = ::t2::map::hdmap;

namespace lane_change_trigger {

enum class LaneChangeDirection : int {
  NONE = 0,
  LEFT = 1,
  RIGHT = 2,
};

std::string ToLaneChangeDirectionString(LaneChangeDirection direction);

enum class TriggerType {
  UNDEFINED = 0,
  MERGING_LANES = 1,
  SWITCHING_LANES = 2,
  VEHICLE_TYPE_RESTRICTION = 3,
};

struct IncreaseDecreaseTrigger {
  TriggerType trigger_type = TriggerType::UNDEFINED;
  double distance_to_lane_change_end = 0.0;
  LaneChangeDirection direction = LaneChangeDirection::NONE;
  hdmap::Id ego_lane_id;
};

}  // namespace lane_change_trigger
}  // namespace t2::planning
