package(default_visibility = ["//visibility:public"])

PLANNING_COPTS = ["-DMODULE_NAME=\\\"planning\\\""]

cc_binary(
    name = "libtruckmaker_component.so",
    linkshared = True,
    linkstatic = False,
    visibility = ["//visibility:public"],
    deps = [":truckmaker_component_lib"],
)

cc_library(
    name = "truckmaker_component_lib",
    srcs = [
        "truckmaker_component.cpp",
    ],
    hdrs = [
        "truckmaker_component.hpp",  # TruckMakerComponent
    ],
    copts = PLANNING_COPTS,
    linkstatic = False,
    deps = [
        ":truckmaker_config",
        ":utility",
        "//src/planning:planning_module_exception",  # PlanningModuleException
        "//src/planning/common:input_message_info",  # ChassisInfo, LocalizationInfo, PlanningProcInput
        "//src/planning/common:json_string",  # ToJsonString, FromJsonString
        "//src/planning/truckmaker/common:truckmaker_data",  # TruckMakerData*
        # ========================
        # Third-party dependencies
        # ========================
        "@apex//common/cpputils",
        "@apex//grace/execution/executor2",
        "@apex//grace/interfaces/std_msgs",
        "@apex//grace/ros/rclcpp/rclcpp",  # rclcpp::Node
    ],
)

cc_library(
    name = "utility",
    srcs = [
        "utility.cpp",
    ],
    hdrs = [
        "utility.hpp",  # ToProto, FromProto
    ],
    copts = PLANNING_COPTS,
    linkstatic = False,
    deps = [
        "//src/interfaces/planning_msgs",  # inter-module planning messages
        "//src/planning:planning_module_exception",  # PlanningModuleException
        "//src/planning/truckmaker/common:truckmaker_data",  # TruckMakerData
    ],
)

cc_library(
    name = "truckmaker_config",
    srcs = [],
    hdrs = [
        "truckmaker_config.hpp",  # TruckMakerConfig
    ],
    copts = PLANNING_COPTS,
    linkstatic = False,
    deps = [
        "@cereal",
    ],
)
