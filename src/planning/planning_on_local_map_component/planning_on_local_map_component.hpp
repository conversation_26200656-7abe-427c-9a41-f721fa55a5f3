// Copyright (c) 2025 T2 Inc. All rights reserved.
#pragma once

#include <perception_msgs/msg/perception_obstacles.hpp>  // PerceptionObstacles
#include <planning_msgs/msg/adc_trajectory.hpp>          // ADCTrajectory
#include <planning_msgs/msg/planning_test_message.hpp>   // PlanningTestMessage

#include "grace/execution/executor2/include/executor2/apex_node_base.hpp"  //
#include "src/planning/planning_macros.hpp"  // REGISTER_*_PLANNING_MSG
#include "src/planning/planning_on_local_map_component/frenet_planner.hpp"  // FrenetPlanner
#include "src/planning/planning_on_local_map_component/internal_data_definition.hpp"
#include "src/planning/planning_on_local_map_component/prototype_planner_interface.hpp"  // PrototypePlanner
// ::apex::executor::apex_node_base

namespace t2::planning {

REGISTER_INTER_PERCEPTION_MSG(PerceptionObstacles);
// Inter-component planning messages
REGISTER_INTER_PLANNING_MSG(ADCTrajectory);
REGISTER_INTER_PLANNING_MSG(PlanningTestMessage);

class PlanningOnLocalMapComponent final : public ::apex::executor::apex_node_base {
  using base = ::apex::executor::apex_node_base;
  using MapPathPoints = std::vector<route_lane_manager::MapPathPoint>;
  rclcpp::Node& node_;

 public:
  PlanningOnLocalMapComponent();
  ~PlanningOnLocalMapComponent();

  void WaitForMatched(const std::size_t expected_num_subscriptions,
                      const std::chrono::nanoseconds timeout = std::chrono::nanoseconds::max())

  {
    trajectory_writer_->wait_for_matched(expected_num_subscriptions, timeout);
  }

  /* ===== Required Apex functions ===== */
  bool execute_impl() override;
  ::apex::executor::subscription_list get_triggering_subscriptions_impl() const override;
  ::apex::executor::subscription_list get_non_triggering_subscriptions_impl() const override;
  ::apex::executor::publisher_list get_publishers_impl() const override;

  ADCTrajectory Plan(const PlanningVehicleState& ego_state,
                     const std::map<int, Obstacle>& obstacles,
                     const RoadInformation& road_information);

  ::rclcpp::Publisher<ADCTrajectory>::SharedPtr CreateTrajectoryWriter(
      const std::string& topic) const;

  const auto& planning_test_writer() const { return planning_test_writer_; }

 private:
  std::map<int, Obstacle> PredictObjectPath(const PlanningVehicleState& ego_state,
                                            const std::map<int, Obstacle>& obstacles,
                                            const RoadInformation& road_information);

  std::shared_ptr<TrajectoryPlannerBase> DecideTrajectoryPlanner(
      const PlanningVehicleState& ego_state, const std::map<int, Obstacle>& obstacles,
      const RoadInformation& road_information, PlannerConstraint& planner_constraint);

  /* ===== Writers =====*/
  ::rclcpp::Publisher<PerceptionObstacles>::SharedPtr perception_writer_;
  ::rclcpp::Publisher<ADCTrajectory>::SharedPtr trajectory_writer_;

  ::rclcpp::Publisher<PlanningTestMessage>::SharedPtr planning_test_writer_;

  /* ===== Readers =====*/
  ::rclcpp::PollingSubscription<PerceptionObstacles>::SharedPtr perception_reader_;

  /* ===== END OF READERS, WRITERS =====*/

  /*===== Topics =====*/
  static constexpr char const* kPerceptionObstacleTopic = "/t2/perception";
  static constexpr char const* kTrajectoryTopic = "/t2/planning";

  static constexpr char const* kPlanningOnLocalMapComponentName = "planning_local_map_component";
  static constexpr char const* kPlanningOnLocalMapNamespace = "planning_local_map_namespace";

  static constexpr char const* kPlanningTestTopic = "/t2/planning/test";

  /* ==== Planner Algorithm ==== */
  std::shared_ptr<FrenetPlanner> frenet_planner_;
  std::shared_ptr<PrototypePlanner> prototype_planner_;
};

}  // namespace t2::planning
