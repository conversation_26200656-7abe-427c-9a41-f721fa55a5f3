#include "simulation.hpp"

namespace t2::planning {

using route_lane_manager::MapPathPoint;

TrajectoryPoint ConstuctTrajectoryPoint(double x, double y, double theta, double v, double a) {
  TrajectoryPoint global_traj_point;
  auto& path_point = global_traj_point.path_point;
  path_point.x = x;
  path_point.y = y;
  path_point.theta = theta;
  global_traj_point.v = v;
  global_traj_point.a = a;
  return global_traj_point;
}

PlanningVehicleState ConstructVehicleState(double x, double y, double theta, double v, double a) {
  PlanningVehicleState vehicle_state;
  vehicle_state.x = x;
  vehicle_state.y = y;
  vehicle_state.heading = theta;
  vehicle_state.linear_velocity = v;
  vehicle_state.linear_acceleration = a;
  return vehicle_state;
}

void GetCenterLaneCurve(std::vector<MapPathPoint>& local_center_lane_curve,
                        const PlanningVehicleState& global_pose,
                        const VectorPairDouble& global_center_lane_curve) {
  const size_t num_points = global_center_lane_curve.size();
  local_center_lane_curve.resize(num_points);

  for (size_t i = 0; i < num_points; ++i) {
    auto [x, y] = global_center_lane_curve[i];  //< central curve in Global

    MapPathPoint& p_l = local_center_lane_curve[i];
    x -= global_pose.x;
    y -= global_pose.y;
    double theta = global_pose.heading;
    double cth = cos(theta), sth = sin(theta);
    p_l.set_x(x * cth + y * sth);
    p_l.set_y(-x * sth + y * cth);
  }
}

PlanningVehicleState UpdateGlobalPose(const TrajectoryPoint& local_traj_point,
                                      PlanningVehicleState global_vehicle_state) {
  const auto& traj_path_point = local_traj_point.path_point;
  const double x = traj_path_point.x;
  const double y = traj_path_point.y;
  const double theta = global_vehicle_state.heading;
  const double cth = cos(theta), sth = sin(theta);

  global_vehicle_state.x += x * cth - y * sth;
  global_vehicle_state.y += x * sth + y * cth;
  global_vehicle_state.heading += traj_path_point.theta;
  global_vehicle_state.linear_velocity = local_traj_point.v;
  global_vehicle_state.linear_acceleration = local_traj_point.a;
  return global_vehicle_state;
}

VectorPairDouble::const_iterator find_begin(const VectorPairDouble& vec, const double x0,
                                            const double x_look_backward_distance) {
  auto it = std::lower_bound(
      vec.cbegin(), vec.cend(), x0 - x_look_backward_distance,
      [](const std::pair<double, double>& p, double value) { return p.first < value; });

  if (it != vec.cbegin()) {
    --it;  // last item where x < x0 - x_look_backward_distance
  } else if (it == vec.cbegin()) {
  } else {
    it = vec.cend();  // No such item found
  }
  return it;
}

VectorPairDouble::const_iterator find_end(const VectorPairDouble& vec, const double x0,
                                          const double x_look_forward_distance) {
  auto it = std::lower_bound(
      vec.cbegin(), vec.cend(), x0 + x_look_forward_distance,
      [](const std::pair<double, double>& p, double value) { return p.first < value; });
  // it now points to first element where x >= x0 + x_look_forward_distance, or vec.end() if none

  T2_INFO << "x0=" << x0 << ", x_look_forward_distance=" << x_look_forward_distance
          << ", out=" << it->first;
  return it;
}

void GetCenterLaneCurve(std::vector<MapPathPoint>& local_center_lane_curve,
                        const PlanningVehicleState& global_pose,
                        VectorPairDouble::const_iterator it_begin,
                        VectorPairDouble::const_iterator it_end) {
  local_center_lane_curve.clear();

  for (auto it = it_begin;; ++it) {
    auto [x, y] = *it;  //< central curve in Global
    x -= global_pose.x;
    y -= global_pose.y;
    double theta = global_pose.heading;
    double cth = cos(theta), sth = sin(theta);

    local_center_lane_curve.emplace_back();
    MapPathPoint& p_l = local_center_lane_curve.back();
    p_l.set_x(x * cth + y * sth);
    p_l.set_y(-x * sth + y * cth);

    if (it == it_end) {
      break;
    }
  }
  T2_INFO << "n_points=" << local_center_lane_curve.size()
          << ", d=" << std::distance(it_begin, it_end) + 1;
}

void GetCenterLaneCurve(std::vector<route_lane_manager::MapPathPoint>& global_center_lane_curve,
                        VectorPairDouble::const_iterator it_begin,
                        VectorPairDouble::const_iterator it_end) {
  global_center_lane_curve.clear();
  for (auto it = it_begin;; ++it) {
    auto [x, y] = *it;  //< central curve in Global
    global_center_lane_curve.emplace_back();
    MapPathPoint& p_l = global_center_lane_curve.back();
    p_l.set_x(x);
    p_l.set_y(y);

    if (it == it_end) {
      break;
    }
  }

  T2_INFO << "n_points=" << global_center_lane_curve.size()
          << ", d=" << std::distance(it_begin, it_end) + 1;
}

void GetCenterLaneCurve(VectorPairDouble& global_center_lane_curve,
                        VectorPairDouble::const_iterator it_begin,
                        VectorPairDouble::const_iterator it_end) {
  global_center_lane_curve.clear();
  for (auto it = it_begin;; ++it) {
    global_center_lane_curve.push_back(*it);
    if (it == it_end) {
      break;
    }
  }
  T2_INFO << "n_points=" << global_center_lane_curve.size()
          << ", d=" << std::distance(it_begin, it_end) + 1;
}

}  // namespace t2::planning
