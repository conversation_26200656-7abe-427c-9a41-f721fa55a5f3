load("@rules_cc//cc:defs.bzl", "cc_binary", "cc_library")

package(default_visibility = ["//visibility:public"])

PLANNING_COPTS = [
    "-DMODULE_NAME=\\\"planning\\\"",
]

cc_binary(
    name = "libplanning_local_component.so",
    linkshared = True,
    linkstatic = False,
    deps = [
        ":local_map_component",
    ],
)

cc_library(
    name = "local_map_component",
    srcs = [
        "planning_on_local_map_component.cpp",
    ],
    hdrs = [
        "planning_on_local_map_component.hpp",  # PlanningOnLocalMapComponent
    ],
    copts = PLANNING_COPTS,
    data = [
        "//src/planning:runtime_data",  # "conf/*.json"
    ],
    deps = [
        "//src/common/core",  # T2_*
        "//src/common/runfiles",  # RuntimeDependencyManager
        "//src/interfaces/perception_msgs",  # inter-module perception messages
        "//src/interfaces/planning_msgs",  # inter-module planning messages
        "//src/planning:planning_macros",  # REGISTER_*_PLANNING_MSG
        "//src/planning/common:json_string",
        "//src/planning/config:planner_config",  # PlannerConfig
        "//src/planning/config:planning_config",  # PlanningConfig
        "//src/planning/planner/path_planner",  # PathPlanner
        "//src/planning/planner/postprocessing",  # PathOptimizer
        "//src/planning/planner/speed_planner",  # SpeedPlanner
        "//src/planning/planning_on_local_map_component:frenet_planner",  # FrenetPlanner
        "//src/planning/planning_on_local_map_component:internal_data_definition",
        "//src/planning/planning_on_local_map_component:prototype_planner_interface",  # FrenetPlanner
        "//src/planning/reference_line",  # ReferenceLine
        "//src/planning/reference_line:reference_line_provider",  # ReferenceLineProvider
        "@apex//grace/execution/executor2",  # ::apex::executor::executable_item
    ],
)

cc_library(
    name = "query_local_map",
    srcs = [
        "query_local_map.cpp",
    ],
    hdrs = [
        "query_local_map.hpp",  # QueryLocalMap
    ],
    copts = PLANNING_COPTS,
    deps = [
        "//src/common/core",  # T2_*
        "//src/interfaces/perception_msgs",  # inter-module perception messages
        "//src/interfaces/planning_msgs",  # inter-module planning messages
        "//src/perception/local_map:local_map_util",  # QueryLocalMap
    ],
)

cc_library(
    name = "simulation",
    srcs = [
        "simulation.cpp",
    ],
    hdrs = [
        "simulation.hpp",
    ],
    copts = PLANNING_COPTS,
    deps = [
        "//src/common/core",  # T2_*
        "//src/interfaces/planning_msgs",  # inter-module planning messages
        "//src/interfaces/planning_trajectory_msgs",  # planning_trajectory_msgs::msg::*
        "//src/planning:planning_macros",  # REGISTER_*_PLANNING_MSG
        "//src/planning/common/planning_vehicle_state",
        "//src/planning/route_lane_manager:path",  # MapPathPoint
    ],
)

cc_library(
    name = "internal_data_definition",
    srcs = [
    ],
    hdrs = [
        "internal_data_definition.hpp",
    ],
    copts = PLANNING_COPTS,
    deps = [
        "//src/planning/route_lane_manager:path",  # MapPathPoint
    ],
)

cc_library(
    name = "frenet_planner",
    srcs = [
        "frenet_planner.cpp",
    ],
    hdrs = [
        "frenet_planner.hpp",
        "trajectory_planner.hpp",
    ],
    copts = PLANNING_COPTS,
    deps = [
        "//src/common/core",  # T2_*
        "//src/common/runfiles",  # RuntimeDependencyManager
        "//src/interfaces/planning_msgs",  # inter-module planning messages
        "//src/interfaces/planning_trajectory_msgs",  # planning_trajectory_msgs::msg::*
        "//src/planning:planning_macros",  # REGISTER_*_PLANNING_MSG
        "//src/planning/config:planner_config",  # PlannerConfig
        "//src/planning/config:planning_config",  # PlanningConfig
        "//src/planning/planner/path_planner",  # PathPlanner
        "//src/planning/planner/postprocessing",  # PathOptimizer
        "//src/planning/planner/speed_planner",  # SpeedPlanner
        "//src/planning/planning_on_local_map_component:internal_data_definition",
        "//src/planning/reference_line",  # ReferenceLine
        "//src/planning/reference_line:reference_line_provider",  # ReferenceLineProvider
        "//src/planning/route_lane_manager:path",  # MapPathPoint
    ],
)

cc_library(
    name = "prototype_planner_interface",
    srcs = [
        "prototype_planner_interface.cpp",
    ],
    hdrs = [
        "prototype_planner_interface.hpp",
        "trajectory_planner.hpp",
    ],
    copts = PLANNING_COPTS,
    deps = [
        "//src/interfaces/planning_msgs",
        "//src/planning:planning_macros",  # REGISTER_*_PLANNING_MSG
        "//src/planning/common:obstacle",
        "//src/planning/common/planning_vehicle_state",
        "//src/planning/planning_on_local_map_component:internal_data_definition",
        "//src/planning/planning_on_local_map_component/prototype_planner",
    ],
)
