#pragma once

#include <planning_trajectory_msgs/msg/trajectory_point.hpp>  // TrajectoryPoint

#include "src/planning/common/planning_vehicle_state/planning_vehicle_state.hpp"
#include "src/planning/planning_macros.hpp"          // REGISTER_*_PLANNING_MSG
#include "src/planning/route_lane_manager/path.hpp"  // route_lane_manager::MapPathPoint

namespace t2::planning {

REGISTER_INTER_TRAJECTORY_MSG(TrajectoryPoint);

using VectorPairDouble = std::vector<std::pair<double, double>>;

TrajectoryPoint ConstuctTrajectoryPoint(double x, double y, double theta, double v, double a);
PlanningVehicleState ConstructVehicleState(double x, double y, double theta, double v, double a);

void GetCenterLaneCurve(std::vector<route_lane_manager::MapPathPoint>& local_center_lane_curve,
                        const PlanningVehicleState& global_pose,
                        const VectorPairDouble& global_center_lane_curve);

PlanningVehicleState UpdateGlobalPose(const TrajectoryPoint& local_traj_point,
                                      PlanningVehicleState global_traj_point);

VectorPairDouble::const_iterator find_begin(const VectorPairDouble& vec, const double x0,
                                            const double x_look_backward_distance);

VectorPairDouble::const_iterator find_end(const VectorPairDouble& vec, const double x0,
                                          const double x_look_forward_distance);

void GetCenterLaneCurve(std::vector<route_lane_manager::MapPathPoint>& local_center_lane_curve,
                        const PlanningVehicleState& global_pose,
                        VectorPairDouble::const_iterator it_begin,
                        VectorPairDouble::const_iterator it_end);

void GetCenterLaneCurve(std::vector<route_lane_manager::MapPathPoint>& global_center_lane_curve,
                        VectorPairDouble::const_iterator it_begin,
                        VectorPairDouble::const_iterator it_end);

void GetCenterLaneCurve(VectorPairDouble& global_center_lane_curve,
                        VectorPairDouble::const_iterator it_begin,
                        VectorPairDouble::const_iterator it_end);

}  // namespace t2::planning
