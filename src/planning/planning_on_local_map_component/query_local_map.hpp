// Copyright (c) 2025 T2 Inc. All rights reserved.
#pragma once

#include <perception_msgs/msg/local_map.hpp>            // LocalMap
#include <planning_msgs/msg/adc_trajectory.hpp>         // ADCTrajectory
#include <planning_trajectory_msgs/msg/path_point.hpp>  // PathPoint

#include "src/common/core/logging.hpp"                  // T2_*
#include "src/perception/local_map/local_map_util.hpp"  // QueryLocalMap

namespace t2::planning {

using PathPoint = ::planning_trajectory_msgs::msg::PathPoint;

std::vector<std::pair<double, double>> QueryCentralCurve(const PathPoint& point);

std::vector<std::pair<double, double>> QueryCentralCurve(const Eigen::Affine3d& world_to_local);
/*
bool QueryLocalMap(const Eigen::Affine3d& world_to_local, LocalMap::BorrowedType& local_map);
*/

}  // namespace t2::planning
