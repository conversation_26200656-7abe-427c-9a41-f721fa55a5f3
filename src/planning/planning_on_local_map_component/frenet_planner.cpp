#include "src/planning/planning_on_local_map_component/frenet_planner.hpp"

#include "src/common/runfiles/runtime_dependency_manager.hpp"  // RuntimeDependencyManager

namespace t2::planning {

template <typename T>
std::string vec_to_string(const std::vector<T>& v, const std::string_view delim = ",") {
  std::stringstream ss;
  ss << std::setprecision(8) << "[";
  for (const auto& vi : v) {
    ss << vi << delim;
  }
  ss << "]";
  return ss.str();
}

FrenetPlanner::FrenetPlanner() {
  LoadConfiguration(planning_config_, planner_config_);
  reference_line_provider_ = std::make_shared<ReferenceLineProvider>();
  path_planner_ = std::make_shared<PathPlanner>(planner_config_, planning_config_);
  speed_planner_ = std::make_shared<SpeedPlanner>(planner_config_, planning_config_);
  path_optimizer_ = std::make_shared<PathOptimizer>(planner_config_, planning_config_);
  const bool with_hdmap = false;
  reference_line_provider_->Init(with_hdmap);
}

void FrenetPlanner::LoadConfiguration(PlanningConfiguration& planning_config,
                                      PlannerConfig& planner_config) const {
  {
    const std::string planning_config_file =
        common::runfiles::RuntimeDependencyManager::ResolvePath(kPlanningConfigFile);
    std::ifstream is(planning_config_file);
    T2_PLAN_CHECK_FULL(PlanningModuleErrorCode::INVALID_INPUT, is)
        << "Cannot read planning config " << planning_config_file;
    cereal::JSONInputArchive archive(is);  // serialize to JSON
    archive(cereal::make_nvp("planning_config", planning_config));
    T2_INFO << "Planning config:\n" << ToJsonString(planning_config);
  }

  {
    const std::string planner_config_file =
        common::runfiles::RuntimeDependencyManager::ResolvePath(kPlannerConfigFile);
    std::ifstream is(planner_config_file);
    T2_PLAN_CHECK_FULL(PlanningModuleErrorCode::INVALID_INPUT, is)
        << "Cannot read " << planner_config_file;
    cereal::JSONInputArchive archive(is);  // serialize to JSON
    archive(CEREAL_NVP(planner_config));
    T2_INFO << "Planner config:\n" << ToJsonString(planner_config);
  }
}

ADCTrajectory FrenetPlanner::Plan(const PlanningVehicleState& ego_state,
                                  const std::map<int, Obstacle>& obstacles,
                                  const RoadInformation& road_information,
                                  const PlannerConstraint& planner_constraint) {
  planning::ReferenceLine raw_reference_line(road_information.lane_center_curves);
  // T2_INFO << "raw_reference_line=" << raw_reference_line.DebugString();

  /*===== ReferenceLineProvider, smoothing =====*/
  // static constexpr char const* kMapDir = "/apollo/modules/map/data/shirosato-kosoku-shukairo";
  // static constexpr char const* kGlobalFlagFile =
  // "/apollo/modules/common/data/global_flagfile.txt";
  // // test::UpdateMapFlagFile(std::string(kMapDir));
  // google::SetCommandLineOption("flagfile", kGlobalFlagFile);

  planning::ReferenceLine reference_line;
  reference_line_provider_->SmoothReferenceLine(raw_reference_line, reference_line);
  T2_INFO << "reference_line=" << reference_line.DebugString();

  /*===== Planning ====*/
  planning::ReferenceLineAndRouteSegments reference_line_obj{
      .reference_line = planning::ReferenceLine(reference_line), .route_segments = {}};
  planning::ReferenceLineMap reference_line_map{
      {planning::ReferenceLineType::CURRENT, reference_line_obj},
      // not sure why, seeGetSuitableReferenceLine
      {planning::ReferenceLineType::LANE_CHANGE_SOURCE, reference_line_obj},
  };

  /*===== IntentionTaskData ====*/
  planning::IntentionTaskData intention_task_data;
  intention_task_data.planning_start_point.path_point.x = ego_state.x;
  intention_task_data.planning_start_point.path_point.y = ego_state.y;
  intention_task_data.planning_start_point.path_point.theta = ego_state.heading;
  intention_task_data.planning_start_point.v = ego_state.linear_velocity;
  intention_task_data.planning_start_point.a = ego_state.linear_acceleration;

  /*
    intention_task_data.is_lane_change = false;
    auto& lane_change_information = intention_task_data.lane_change_information;
    auto& planning_module_status = intention_task_data.planning_module_status;
    planning_module_status.set_state(planning::PlanningMessages_PlanningModuleState_LANE_FOLLOW);
  */

  /*===== Planners =====*/
  path_planner_->ExecuteTask(reference_line_map, intention_task_data);

  std::vector<double>& path_reference_l =
      intention_task_data.path_reference_l;  ///< output of PathPlanner

  T2_INFO << "Call ComputeLateralValues......";

  path_planner_->ComputeLateralValues(
      std::vector<planning::PathBoundPoint>(planner_config_.path_planner_config.num_steps + 1,
                                            planning::PathBoundPoint{
                                                .s = 0.0,
                                                .l_min = -1.75,  // right
                                                .l_max = 1.75,   // left
                                            }),
      0);

  // T2_INFO << "path_reference_l=" << vec_to_string(path_reference_l);

  speed_planner_->ExecuteTask(reference_line_map, intention_task_data);

  path_optimizer_->ExecuteTask(reference_line_map, intention_task_data);

  // for (auto& traj_point : intention_task_data.discretized_trajectory) {
  //   // T2_INFO << traj_point.ShortDebugString();
  //   T2_INFO << "v = " << traj_point.v << ", a = " << traj_point.a;
  // }

  ADCTrajectory trajectory;  // TODO: set creation_timestamp, and sequence_number
  trajectory.trajectory_point = intention_task_data.discretized_trajectory;
  trajectory.only_trajectory = intention_task_data.discretized_trajectory;
  return trajectory;
}

}  // namespace t2::planning
