#include "src/planning/planning_on_local_map_component/prototype_planner_interface.hpp"

namespace t2::planning {

ADCTrajectory PrototypePlanner::Plan(const PlanningVehicleState& ego_state,
                                     const std::map<int, Obstacle>& obstacles,
                                     const RoadInformation& road_information,
                                     const PlannerConstraint& planner_constraint) {
  const Params params{
      .N = planner_constraint.N, .dt = planner_constraint.dt, .v_ref = planner_constraint.v_ref};
  if (trajectory) {
    d0 = trajectory->delta_sol[1];
  }
  Vehicle vehicle(ego_state.x, ego_state.y, ego_state.heading, ego_state.linear_velocity,
                  ego_state.linear_acceleration, d0);

  std::vector<std::pair<double, double>> center_line;
  center_line.reserve(road_information.lane_center_curves.size());
  for (const auto& map_point : road_information.lane_center_curves) {
    center_line.emplace_back(map_point.x(), map_point.y());
  }

  trajectory = ComputePlan(vehicle, center_line, trajectory, params);

  ADCTrajectory adc_trajectory;
  auto& only_trajectory = adc_trajectory.only_trajectory;
  only_trajectory.reserve(trajectory->x_sol.size());
  for (size_t i = 0; i < trajectory->x_sol.size(); ++i) {
    only_trajectory.emplace_back();
    auto& trajectory_point = only_trajectory.back();
    trajectory_point.path_point.x = trajectory->x_sol[i];
    trajectory_point.path_point.y = trajectory->y_sol[i];
    trajectory_point.path_point.theta = trajectory->theta_sol[i];
    trajectory_point.v = trajectory->v_sol[i];
    trajectory_point.a = trajectory->a_sol[i];
  }

  return adc_trajectory;
}

}  // namespace t2::planning
