#pragma once

#include <string>
#include <vector>

#include "src/planning/route_lane_manager/path.hpp"

namespace t2::planning {

using MapPathPoints = std::vector<route_lane_manager::MapPathPoint>;

struct RoadInformation {
  MapPathPoints lane_center_curves;  // Local coordinate
  std::vector<double> target_speeds;
  // TODO
};

struct PlannerConstraint {
  double dt;     // Trajectory sampling time
  int N;         // The number of points in the trajectory
  double v_ref;  // The target speed that the planner should follow
};

}  // namespace t2::planning
