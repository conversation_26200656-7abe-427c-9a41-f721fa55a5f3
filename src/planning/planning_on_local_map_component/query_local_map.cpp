#include "query_local_map.hpp"

namespace t2::planning {

using LocalMap = ::perception_msgs::msg::LocalMap;

// REGISTER_INTER_PREDICTION_MSG(LocalLaneSegment);

std::vector<std::pair<double, double>> QueryCentralCurve(const Eigen::Affine3d& world_to_local) {
  // Eigen::Vector3d translation = world_to_local.translation();
  // Eigen::Matrix3d rotation_matrix = transform.rotation();
  // Eigen::Quaterniond quaternion(transform.rotation());

  LocalMap local_map;
  perception::local_map::QueryLocalMap(world_to_local, local_map);

  std::vector<std::pair<double, double>> central_curve;
  // LocalLaneSegment.idl, LocalMap.idl
  static constexpr size_t reserved_num_points = 100 * 1000;
  central_curve.reserve(reserved_num_points);

  for (const auto& lane_segment : local_map.local_lane_segments) {
    for (const auto& point : lane_segment.central_curve) {
      central_curve.push_back({point.x, point.y});
    }
  }

  T2_INFO << "Number of lines in locap: " << local_map.local_lane_segments.size()
          << "; number of points in the output central curve: " << central_curve.size();

  return central_curve;
}

std::vector<std::pair<double, double>> QueryCentralCurve(const PathPoint& point) {
  Eigen::Affine3d truck_in_global = Eigen::Affine3d::Identity();

  // Set translation
  truck_in_global.translation() = Eigen::Vector3d(point.x, point.y, point.z);  ///< point.z is 0

  // Set yaw rotation (theta in radians)
  truck_in_global.linear() =
      Eigen::AngleAxisd(point.theta, Eigen::Vector3d::UnitZ()).toRotationMatrix();

  return QueryCentralCurve(truck_in_global);
}

}  // namespace t2::planning
