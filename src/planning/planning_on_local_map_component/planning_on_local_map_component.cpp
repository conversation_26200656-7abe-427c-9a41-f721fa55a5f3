// Copyright (c) 2025 T2 Inc. All rights reserved.
#include "src/planning/planning_on_local_map_component/planning_on_local_map_component.hpp"  // PlanningOnLocalMapComponent

#include "src/common/runfiles/runtime_dependency_manager.hpp"  // RuntimeDependencyManager

namespace t2::planning {

PlanningOnLocalMapComponent::PlanningOnLocalMapComponent()
    : base(kPlanningOnLocalMapComponentName, kPlanningOnLocalMapNamespace),
      node_(get_rclcpp_node()) {
  /* ===== Readers =====*/
  perception_reader_ = node_.create_polling_subscription<PerceptionObstacles>(
      kPerceptionObstacleTopic, rclcpp::DefaultQoS());

  /* ===== Writers =====*/
  perception_writer_ =
      node_.create_publisher<PerceptionObstacles>(kPerceptionObstacleTopic, rclcpp::DefaultQoS());

  trajectory_writer_ = CreateTrajectoryWriter(kTrajectoryTopic);

  planning_test_writer_ = node_.create_publisher<PlanningTestMessage>(
      kPlanningTestTopic,
      rclcpp::DefaultQoS()
          .keep_last(100)
          .resource_limits_max_non_self_contained_type_serialized_size(500 * 1024  // 500 KiB
                                                                       ));

  frenet_planner_ = std::make_shared<FrenetPlanner>();
  prototype_planner_ = std::make_shared<PrototypePlanner>();
}

::rclcpp::Publisher<ADCTrajectory>::SharedPtr PlanningOnLocalMapComponent::CreateTrajectoryWriter(
    const std::string& topic) const {
  return node_.create_publisher<ADCTrajectory>(
      topic, rclcpp::DefaultQoS{}
                 .keep_last(100)
                 .resource_limits_max_non_self_contained_type_serialized_size(500 * 1024  // 500 KiB
                                                                              ));
}

PlanningOnLocalMapComponent::~PlanningOnLocalMapComponent() {}

::apex::executor::subscription_list PlanningOnLocalMapComponent::get_triggering_subscriptions_impl()
    const {
  return {perception_reader_};
}

::apex::executor::subscription_list
PlanningOnLocalMapComponent::get_non_triggering_subscriptions_impl() const {
  return {perception_reader_};
}

::apex::executor::publisher_list PlanningOnLocalMapComponent::get_publishers_impl() const {
  return {perception_writer_};
}

bool PlanningOnLocalMapComponent::execute_impl() {
  auto perception_msgs{perception_reader_->take()};

  for (const auto& msg : perception_msgs) {
    if (msg.info().valid()) {
      // [[maybe_unused]] auto adc_trajectory = Plan(msg.data());  // TODO
    }
  }
  return true;
}

std::map<int, Obstacle> PlanningOnLocalMapComponent::PredictObjectPath(
    const PlanningVehicleState& ego_state, const std::map<int, Obstacle>& obstacles,
    const RoadInformation& road_information) {
  // TODO: call Prediction algorithm here
  std::map<int, Obstacle> predicted_obstacles;
  return predicted_obstacles;
}

std::shared_ptr<TrajectoryPlannerBase> PlanningOnLocalMapComponent::DecideTrajectoryPlanner(
    const PlanningVehicleState& ego_state, const std::map<int, Obstacle>& obstacles,
    const RoadInformation& road_information, PlannerConstraint& planner_constraint) {
  // TODO: call Behavior Planner algorithm here

  // TODO: replace magic number by Behavior Planner call
  planner_constraint.N = 60;
  planner_constraint.dt = 0.1;
  planner_constraint.v_ref = 22.00;

  std::shared_ptr<TrajectoryPlannerBase> trajectory_planner = frenet_planner_;
  return trajectory_planner;
}

ADCTrajectory PlanningOnLocalMapComponent::Plan(const PlanningVehicleState& ego_state,
                                                const std::map<int, Obstacle>& obstacles,
                                                const RoadInformation& road_information) {
  auto predicted_obstacles = PredictObjectPath(ego_state, obstacles, road_information);

  PlannerConstraint planner_constraint;
  auto trajectory_planner =
      DecideTrajectoryPlanner(ego_state, predicted_obstacles, road_information, planner_constraint);

  auto trajectory = trajectory_planner->Plan(ego_state, predicted_obstacles, road_information,
                                             planner_constraint);
  return trajectory;
}

}  // namespace t2::planning
