// Copyright (c) 2025 T2 Inc. All rights reserved.
#pragma once

#include <iomanip>
#include <sstream>

namespace t2::planning {

class Vehicle {
 public:
  Vehicle(double x, double y, double theta, double v, double a, double delta)
      : x(x), y(y), theta(theta), v(v), a(a), delta(delta) {}

  Vehicle(const Vehicle& rhs) = default;
  Vehicle(Vehicle&& rhs) = default;
  Vehicle& operator=(const Vehicle& rhs) {
    x = rhs.x;
    y = rhs.y;
    theta = rhs.theta;
    v = rhs.v;
    a = rhs.a;
    delta = rhs.delta;
    return *this;
  }

  std::string to_string() const {
    std::stringstream ss;
    ss << std::setprecision(16) << "x=" << x << ", y=" << y << ", theta=" << theta << ", v=" << v
       << ", a=" << a << ", delta=" << delta << ", lr=" << lr << ", lf=" << lf;
    return ss.str();
  }

  double x = 0.0;      ///< x in Global
  double y = 0.0;      ///< y in Global
  double theta = 0.0;  ///< heading
  double v = 0.0;      ///< velocity [m/s]
  double a = 0.0;      ///< acceleration [m/s^2]
  double delta = 0.0;  ///< steering

  const double lr = 3.815;
  const double lf = 8.1;
};

}  // namespace t2::planning
