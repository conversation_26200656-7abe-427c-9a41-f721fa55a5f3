// Copyright (c) 2025 T2 Inc. All rights reserved.
#pragma once

#include <optional>

#include "utils.hpp"    // generate_centerline
#include "vehicle.hpp"  // Vehicle

namespace t2::planning {

struct CartesianTrajectory {
  std::vector<double> x_sol;
  std::vector<double> y_sol;
  std::vector<double> theta_sol;
  std::vector<double> s_sol;
  std::vector<double> v_sol;
  std::vector<double> a_sol;
  std::vector<double> delta_sol;
};

struct Params {
  int N;         // number of points
  double dt;     // sampling time
  double v_ref;  // target velocity
};

CartesianTrajectory transform_trajectory_local_to_global(
    const CartesianTrajectory& local_trajectory, double x, double y, double theta);

CartesianTrajectory ComputePlan(const Vehicle& vehicle,
                                const std::vector<std::pair<double, double>>& center_line,
                                std::optional<CartesianTrajectory> init_traj, Params params);

}  // namespace t2::planning
