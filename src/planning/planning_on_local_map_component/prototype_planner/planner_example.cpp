// Copyright (c) 2025 T2 Inc. All rights reserved.
#include <casadi/casadi.hpp>

#include "prototype_planner.hpp"

namespace t2_planning = t2::planning;
using VectorPairDouble = std::vector<std::pair<double, double>>;

int main(int argc, char** argv) {
  // Initialize CasADi that is required for bazel build system
  casadi::GlobalOptions::casadipath = "external/casadi/libcasadi/lib";

  // center line parameters
  const double center_line_length = 200.0;  // [m]
  const double center_line_sampling = 5.0;  // [m]

  // Vehicle -> stores state
  const double theta0 = 0.65, v0 = 22.0;
  t2_planning::Vehicle vehicle(0.0, 0.0, theta0, v0, 0.0, 0.0);

  // std::cout << "lr=" << vehicle.lr << ", lf=" << vehicle.lf << ", theta=" << vehicle.theta
  //           << ", v0=" << vehicle.v << ", x=" << vehicle.x << ", y=" << vehicle.y << std::endl;

  // Planner parameters
  const t2::planning::Params params{.N = 60, .dt = 0.1, .v_ref = v0};

  /* ========== Main loop ========== */

  const size_t max_iter = 500;
  size_t iter = 0;
  // stores previous trajectory
  std::optional<t2_planning::CartesianTrajectory> local_trajectory;
  for (; iter < max_iter; ++iter) {
    // Create the CenterLine
    VectorPairDouble center_line = t2_planning::generate_centerline(
        vehicle.x - 50.0, center_line_length, center_line_sampling);

    // Transform global reference line to local coordonate system
    VectorPairDouble local_centerline = t2_planning::transform_centerline_global_to_local(
        center_line, vehicle.x, vehicle.y, vehicle.theta);

    // Compute the trajectory
    local_trajectory = ComputePlan(vehicle, local_centerline, local_trajectory, params);

    // Transform local trajectory to global trajectory
    const t2_planning::CartesianTrajectory global_trajectory =
        t2_planning::transform_trajectory_local_to_global(local_trajectory.value(), vehicle.x,
                                                          vehicle.y, vehicle.theta);

    // Store the state
    vehicle = t2_planning::Vehicle(global_trajectory.x_sol[1], global_trajectory.y_sol[1],
                                   vehicle.theta + global_trajectory.theta_sol[1],
                                   global_trajectory.v_sol[1], global_trajectory.a_sol[1],
                                   global_trajectory.delta_sol[1]);
  }

  return 0;
}
