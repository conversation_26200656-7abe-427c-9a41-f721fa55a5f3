// Copyright (c) 2025 T2 Inc. All rights reserved.
#pragma once

#include <cmath>
#include <tuple>
#include <vector>

namespace t2::planning {

std::vector<std::pair<double, double>> generate_centerline(double x0, double length_m,
                                                           double resolution);

std::vector<std::pair<double, double>> transform_centerline_global_to_local(
    const std::vector<std::pair<double, double>>& global_centerline, double x, double y,
    double theta);

std::vector<double> hypot_vector(const std::vector<double>& X, const std::vector<double>& Y);

std::vector<double> diff(const std::vector<double>& vec);

std::vector<double> cumsum(const std::vector<double>& vec);

}  // namespace t2::planning
