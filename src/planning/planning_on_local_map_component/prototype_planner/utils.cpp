// Copyright (c) 2025 T2 Inc. All rights reserved.
#pragma once

#include "utils.hpp"

namespace t2::planning {

using VectorPairDouble = std::vector<std::pair<double, double>>;

VectorPairDouble generate_centerline(double x0, double length_m, double resolution) {
  VectorPairDouble center_line;

  for (double value = x0; value < x0 + length_m; value += resolution) {
    center_line.emplace_back(value, 40.0 * sin(0.02 * value));
  }

  return center_line;
}

VectorPairDouble transform_centerline_global_to_local(
    const std::vector<std::pair<double, double>>& global_centerline, double x, double y,
    double theta) {
  VectorPairDouble local_centerline(global_centerline.size());
  for (size_t i = 0; i < global_centerline.size(); i++) {
    const double dx = global_centerline[i].first - x;
    const double dy = global_centerline[i].second - y;
    local_centerline[i].first = cos(theta) * dx + sin(theta) * dy;
    local_centerline[i].second = -sin(theta) * dx + cos(theta) * dy;
  }
  return local_centerline;
}

std::vector<double> hypot_vector(const std::vector<double>& X, const std::vector<double>& Y) {
  std::vector<double> values(X.size(), 0.0);
  for (size_t i = 0; i < X.size(); i++) {
    values[i] = hypot(X[i], Y[i]);
  }
  return values;
}

std::vector<double> diff(const std::vector<double>& vec) {
  std::vector<double> values(vec.size() - 1);
  for (size_t i = 0; i < vec.size() - 1; i++) {
    values[i] = vec[i + 1] - vec[i];
  }
  return values;
}

std::vector<double> cumsum(const std::vector<double>& vec) {
  std::vector<double> values(vec.size() + 1, 0.0);
  for (size_t i = 0; i < vec.size(); ++i) {
    values[i + 1] = values[i] + vec[i];
  }
  return values;
}

}  // namespace t2::planning
