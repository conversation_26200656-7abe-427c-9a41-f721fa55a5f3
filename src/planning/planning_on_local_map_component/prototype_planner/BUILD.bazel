load("@rules_cc//cc:defs.bzl", "cc_binary", "cc_library")

package(default_visibility = ["//visibility:public"])

PLANNING_COPTS = [
    "-DMODULE_NAME=\\\"planning\\\"",
]

cc_binary(
    name = "planner_example",
    srcs = [
        "planner_example.cpp",
    ],
    copts = PLANNING_COPTS,
    linkopts = [
        "-lGL",
        "-pthread",
    ],
    deps = [
        ":prototype_planner",
    ],
)

cc_library(
    name = "prototype_planner",
    srcs = [
        "prototype_planner.cpp",
        "utils.cpp",
    ],
    hdrs = [
        "prototype_planner.hpp",
        "utils.hpp",
        "vehicle.hpp",
    ],
    copts = PLANNING_COPTS,
    deps = [
        "@casadi",
    ],
)
