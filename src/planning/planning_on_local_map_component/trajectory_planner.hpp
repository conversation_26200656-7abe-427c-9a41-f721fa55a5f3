#pragma once

#include <planning_msgs/msg/adc_trajectory.hpp>  // ADCTrajectory

#include "src/planning/planning_on_local_map_component/internal_data_definition.hpp"

namespace t2::planning {

REGISTER_INTER_PLANNING_MSG(ADCTrajectory);

class TrajectoryPlannerBase {
 public:
  virtual ~TrajectoryPlannerBase() {};
  virtual ADCTrajectory Plan(const PlanningVehicleState& ego_state,
                             const std::map<int, Obstacle>& obstacles,
                             const RoadInformation& road_information,
                             const PlannerConstraint& planner_constraint) = 0;
};

}  // namespace t2::planning
