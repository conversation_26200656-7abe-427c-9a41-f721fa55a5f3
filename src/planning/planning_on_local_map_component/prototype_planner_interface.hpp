#pragma once

#include <planning_msgs/msg/adc_trajectory.hpp>  // ADCTrajectory

#include "src/planning/common/obstacle.hpp"
#include "src/planning/common/planning_vehicle_state/planning_vehicle_state.hpp"
#include "src/planning/planning_macros.hpp"
#include "src/planning/planning_on_local_map_component/prototype_planner/prototype_planner.hpp"
#include "src/planning/planning_on_local_map_component/trajectory_planner.hpp"  //TrajectoryPlannerBase

namespace t2::planning {

REGISTER_INTER_PLANNING_MSG(ADCTrajectory);

class PrototypePlanner : public TrajectoryPlannerBase {
 public:
  PrototypePlanner() {};
  ADCTrajectory Plan(const PlanningVehicleState& ego_state,
                     const std::map<int, Obstacle>& obstacles,
                     const RoadInformation& road_information,
                     const PlannerConstraint& planner_constraint) override;

  std::optional<CartesianTrajectory> trajectory;
  double d0 = 0.0;
};

}  // namespace t2::planning
